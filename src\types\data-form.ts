import React from "react"
import { DefaultValues } from "react-hook-form"
import type { z } from "zod"

import { DataFormFieldCheckboxProps } from "@/components/data-form/data-form-field-checkbox"
import { DataFormFieldFileUploadProps } from "@/components/data-form/data-form-field-file-upload"
import { DataFormFieldInputProps } from "@/components/data-form/data-form-field-input"
import { DataFormFieldPhoneInputProps } from "@/components/data-form/data-form-field-phone-input"
import { DataFormFieldSelectProps } from "@/components/data-form/data-form-field-select"
import { DataFormFieldTagsInputProps } from "@/components/data-form/data-form-field-tags-input"
import { DataFormFieldTextareaProps } from "@/components/data-form/data-form-field-textarea"

// واجهة لتحديد قيم النموذج
interface DataFormValues {
  [key: string]: any
}

/**
 * خصائص عامة تأخذها جميع حقول مكون النموذج
 */
interface DataFormFieldProps<T = string> {
  name: T
  label: string
  placeholder?: string
  description?: string
  required?: boolean
  disabled?: boolean
}

export type Variants =
  | "input"
  | "textarea"
  | "select"
  | "fileUpload"
  | "tagsInput"
  | "phoneInput"
  | "checkbox"

type SetVariant<T, V extends Variants> = { variant: V } & T

// خيارات للحقول التي تدعم الاختيارات المتعددة
interface DataFormFieldOption<TValue> {
  label: string
  value: TValue
  icon?: React.FC<React.SVGProps<SVGSVGElement>>
  disabled?: boolean
}

// onSubmit handler
type DataFormSubmitAction<TData extends DataFormValues> = (
  values: TData
) => Promise<DataFormSubmitResponse<TData>>

// return onSubmit
type DataFormSubmitResponse<TData extends DataFormValues> = {
  success: boolean
  message?: string
  errors?: z.typeToFlattenedError<TData>["fieldErrors"]
}

// تعريف حقل النموذج
type DataFormField<TData extends DataFormValues> = {
  [K in keyof TData]:
    | SetVariant<DataFormFieldInputProps<K & string>, "input">
    | SetVariant<DataFormFieldTextareaProps<K & string>, "textarea">
    | SetVariant<DataFormFieldPhoneInputProps<K & string>, "phoneInput">
    | SetVariant<DataFormFieldSelectProps<K & string, TData[K]>, "select">
    | SetVariant<DataFormFieldFileUploadProps<K & string>, "fileUpload">
    | SetVariant<DataFormFieldTagsInputProps<K & string>, "tagsInput">
    | SetVariant<DataFormFieldCheckboxProps<K & string>, "checkbox">
}[keyof TData]

interface DataFormPropsOptions {
  /**
   * تعطيل جميع حقول النموذج
   */
  disableAllFields?: boolean
  /**
   * تخصيص زر الإرسال
   */
  submitButton?: {
    label?: string
    className?: string
  }
  /**
   * تخصيص زر إعادة التعيين
   */
  resetButton?: {
    label?: string
    className?: string
    onClick?: () => void
  }
  /**
   * استراتيجية التحقق من الصحة
   * @default "onSubmit"
   */
  validationMode?: "onBlur" | "onChange" | "onSubmit" | "onTouched" | "all"
  /**
   * استراتيجية إعادة التقديم
   * @default onChange
   */
  reValidateMode?: "onBlur" | "onChange" | "onSubmit"
}

interface DataFormProps<TData extends DataFormValues = DataFormValues> {
  className?: string
  /**
   * تحديد ما إذا كان النموذج للإنشاء أو التعديل
   */
  mode: "create" | "update"
  /**
   * تعريف الحقول
   */
  fields: DataFormField<TData>[]
  /**
   * مخطط التحقق من صحة البيانات باستخدام Zod
   */
  schema: z.ZodType<any, any>
  /**
   * القيم الحالية لكل حقول النموذج. الفائدة منها عند استخدام النموذج في وضع التعديل. اذا كان النموذج في وضع إنشاء البيانات الجديدة يفضل استخدام defaultValue في حقل النموذج مباشرة
   */
  defaultValues?: DefaultValues<TData>
  /**
   * دالة يتم استدعاؤها عند تقديم النموذج
   */
  onSubmitAction: DataFormSubmitAction<TData>
  /**
   * خيارات إضافية لمزيد من التخصيص
   */
  options?: DataFormPropsOptions
}

export type {
  DataFormFieldProps,
  DataFormValues,
  DataFormFieldOption,
  DataFormSubmitAction,
  DataFormSubmitResponse,
  DataFormPropsOptions,
  DataFormField,
  DataFormProps,
}
