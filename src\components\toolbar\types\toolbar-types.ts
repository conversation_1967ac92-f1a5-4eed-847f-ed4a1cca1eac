import type * as React from "react"

import { Direction, Locale } from "@/types/globals"

import type { getToolbarDictionary } from "../lib"

export type ToolbarDictionaries = Awaited<
  ReturnType<typeof getToolbarDictionary>
>

export interface ToolbarProps {
  /**
   * Callback function to start a transition.
   *
   * @example
   * ```tsx
   * const [isPending, startTransition] = useTransition()
   *
   * <Toolbar startTransition={startTransition}>
   *   ...
   * </Toolbar>
   * ```
   */
  startTransition?: React.TransitionStartFunction
  /**
   * The direction of the toolbar.
   *
   * @default "rtl"
   */
  dir?: Direction
  /**
   * The locale of the toolbar.
   *
   * @default "ar"
   */
  locale?: Locale
  children?: React.ReactNode
  className?: string
}

export interface ToolbarContextValue {
  dictionaries: ToolbarDictionaries
  dir: Direction
  startTransition?: React.TransitionStartFunction
}
