const toolbarDictionariesEn = {
  filter: {
    triggerButtonLabel: "Filter",
    popover: {
      title: {
        noFilters: "No filters applied",
        withFilters: "Filters",
      },
      description: {
        noFilters: "Add filters to refine your rows.",
        withFilters: "Modify filters to refine your rows.",
      },
      buttonLabels: {
        addFilter: "Add filter",
        resetFilters: "Reset filters",
      },
    },
    filterItem: {
      JoinSelector: {
        where: "Where",
        AND: "AND",
        OR: "OR",
      },
      itemSelector: {
        searchPlaceholder: "Search fields...",
        noFieldsFound: "No fields found.",
      },
      operatorSelector: {
        searchPlaceholder: "Search operators...",
        noOperatorsFound: "No operators found.",
        operators: {
          text: {
            iLike: "Contains",
            notILike: "Does not contain",
            eq: "Is",
            ne: "Is not",
            isEmpty: "Is empty",
            isNotEmpty: "Is not empty",
          },
          number: {
            eq: "Is",
            ne: "Is not",
            lt: "Is less than",
            lte: "Is less than or equal to",
            gt: "Is greater than",
            gte: "Is greater than or equal to",
            isBetween: "Is between",
            isEmpty: "Is empty",
            isNotEmpty: "Is not empty",
          },
          date: {
            eq: "Is",
            ne: "Is not",
            lt: "Is before",
            lte: "Is on or before",
            gt: "Is after",
            gte: "Is on or after",
            isBetween: "Is between",
            isEmpty: "Is empty",
            isNotEmpty: "Is not empty",
          },
          boolean: {
            eq: "Is",
            ne: "Is not",
          },
          select: {
            eq: "Is",
            ne: "Is not",
            isEmpty: "Is empty",
            isNotEmpty: "Is not empty",
          },
          multiSelect: {
            inArray: "Has any of",
            notInArray: "Has none of",
            isEmpty: "Is empty",
            isNotEmpty: "Is not empty",
          },
        },
      },
      valueFilter: {
        searchPlaceholder: "Search values...",
        noValuesFound: "No values found.",
      },
    },
  },
  sorting: {
    triggerButtonLabel: "Sorting",
    popover: {
      title: {
        noSorting: "No sorting applied",
        withSorting: "Sorting",
      },
      description: "Add sorting to organize your rows.",
      buttonLabels: {
        addSorting: "Add sorting",
        resetSorting: "Reset sorting",
      },
    },
    sortingItem: {
      fieldSelector: {
        searchPlaceholder: "Search fields...",
        noFieldsFound: "No fields found.",
      },
      directionSelector: {
        asc: "Ascending",
        desc: "Descending",
      },
    },
  },
}

export default toolbarDictionariesEn
