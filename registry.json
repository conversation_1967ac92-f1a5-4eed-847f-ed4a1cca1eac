{"$schema": "https://ui.shadcn.com/schema/registry.json", "homepage": "https://localhost:3000", "name": "dashboard", "description": "A dashboard built with shadcn/ui.", "items": [{"title": "<PERSON><PERSON><PERSON>", "name": "toolbar", "type": "registry:block", "description": "A toolbar component for filtering, searching, and sorting data", "files": [{"type": "registry:component", "path": "src/components/toolbar/toolbar.tsx", "target": "src/components/toolbar/toolbar.tsx"}, {"type": "registry:component", "path": "src/components/toolbar/toolbar-filter.tsx", "target": "src/components/toolbar/toolbar-filter.tsx"}, {"type": "registry:component", "path": "src/components/toolbar/toolbar-search.tsx", "target": "src/components/toolbar/toolbar-search.tsx"}, {"type": "registry:component", "path": "src/components/toolbar/toolbar-sorting.tsx", "target": "src/components/toolbar/toolbar-sorting.tsx"}, {"type": "registry:file", "path": "src/components/toolbar/index.ts", "target": "src/components/toolbar/index.ts"}, {"type": "registry:lib", "path": "src/components/toolbar/lib/toolbar-lib.ts", "target": "src/components/toolbar/lib/toolbar-lib.ts"}, {"type": "registry:lib", "path": "src/components/toolbar/lib/toolbar-filter-lib.ts", "target": "src/components/toolbar/lib/toolbar-filter-lib.ts"}, {"type": "registry:lib", "path": "src/components/toolbar/lib/toolbar-search-lib.ts", "target": "src/components/toolbar/lib/toolbar-search-lib.ts"}, {"type": "registry:lib", "path": "src/components/toolbar/lib/toolbar-sorting-lib.ts", "target": "src/components/toolbar/lib/toolbar-sorting-lib.ts"}, {"type": "registry:file", "path": "src/components/toolbar/lib/index.ts", "target": "src/components/toolbar/lib/index.ts"}, {"type": "registry:file", "path": "src/components/toolbar/locales/ar.ts", "target": "src/components/toolbar/locales/ar.ts"}, {"type": "registry:file", "path": "src/components/toolbar/locales/en.ts", "target": "src/components/toolbar/locales/en.ts"}, {"type": "registry:file", "path": "src/components/toolbar/types/toolbar-types.ts", "target": "src/components/toolbar/types/toolbar-types.ts"}, {"type": "registry:file", "path": "src/components/toolbar/types/toolbar-filter-types.ts", "target": "src/components/toolbar/types/toolbar-filter-types.ts"}, {"type": "registry:file", "path": "src/components/toolbar/types/toolbar-sorting-types.ts", "target": "src/components/toolbar/types/toolbar-sorting-types.ts"}, {"type": "registry:file", "path": "src/components/toolbar/types/index.ts", "target": "src/components/toolbar/types/index.ts"}, {"type": "registry:hook", "path": "src/components/toolbar/hooks/use-toolbar.ts", "target": "src/components/toolbar/hooks/use-toolbar.ts"}, {"type": "registry:hook", "path": "src/components/toolbar/hooks/use-filter-state.ts", "target": "src/components/toolbar/hooks/use-filter-state.ts"}, {"type": "registry:file", "path": "src/components/toolbar/hooks/index.ts", "target": "src/components/toolbar/hooks/index.ts"}, {"type": "registry:file", "path": "src/components/toolbar/contexts/create-contexts.ts", "target": "src/components/toolbar/contexts/create-contexts.ts"}, {"type": "registry:file", "path": "src/components/toolbar/contexts/index.ts", "target": "src/components/toolbar/contexts/index.ts"}]}]}