import { faker } from "@faker-js/faker"

import { PrismaClient } from "./generated/index.js"

const prisma = new PrismaClient()

async function main() {
  await prisma.admin.deleteMany()

  const roles = ["SUPER_ADMIN", "ADMI<PERSON>", "<PERSON><PERSON><PERSON>AT<PERSON>", "STAFF"]
  const paths = ["/settings", "/dashboard", "/admin/roles", "/users", "/logs"]

  for (let i = 0; i < 10; i++) {
    const name = faker.person.fullName()
    const email = faker.internet.email({ firstName: name.split(" ")[0] })
    const password = faker.internet.password() // غير مشفر، للتجريب فقط
    const role = roles[Math.floor(Math.random() * roles.length)]
    const blockedPaths = faker.helpers.arrayElements(
      paths,
      Math.floor(Math.random() * paths.length)
    )

    await prisma.admin.create({
      data: {
        name,
        email,
        password,
        role,
        isActive: faker.datatype.boolean(),
        profileImageUrl: faker.image.avatar(),
        phone: faker.phone.number("+966 5#######"),
        blockedPaths,
        lastLogin: faker.date.recent(),
        createdAt: faker.date.recent(),
        createdBy: "seed-script",
        updatedBy: "seed-script",
      },
    })
  }

  console.log("✅ تم توليد بيانات وهمية لجدول Admin.")
}

main()
  .catch((e) => {
    console.error("❌ خطأ أثناء seeding:", e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
