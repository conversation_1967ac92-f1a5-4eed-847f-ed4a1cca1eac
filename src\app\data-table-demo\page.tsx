import React from "react"
import prisma from "@/db/prisma"
import { SearchParams } from "nuqs/server"

import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton"
import { getToolbarQueryParser } from "@/components/toolbar"

import DataTableDemo from "./data-table-demo"

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

const getData = async (searchParams: Promise<SearchParams>) => {
  await sleep(1000)
  const { orderBy, where } = await getToolbarQueryParser(searchParams)
  return prisma.admin.findMany({ where, orderBy })
}

export default async function DataTableDemoPage(props: {
  searchParams: Promise<SearchParams>
}) {
  const data = getData(props.searchParams)

  return (
    <div>
      <React.Suspense
        fallback={<DataTableSkeleton columnCount={4} filterCount={1} />}
      >
        <DataTableDemo data={data} />
      </React.Suspense>
    </div>
  )
}
