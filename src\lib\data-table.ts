import type { Column } from "@tanstack/react-table"
import { addDays, endOfDay, startOfDay } from "date-fns"

import type {
  ExtendedColumnFilter,
  FilterOperator,
  FilterVariant,
} from "@/types/data-table"
import { dataTableConfig } from "@/config/data-table"
import { Jo<PERSON><PERSON>per<PERSON> } from "@/components/unstable-data-filter/data-filter-types"

import { FilterItemSchema } from "./parsers"

export function getCommonPinningStyles<TData>({
  column,
  withBorder = false,
}: {
  column: Column<TData>
  withBorder?: boolean
}): React.CSSProperties {
  const isPinned = column.getIsPinned()
  const isLastLeftPinnedColumn =
    isPinned === "left" && column.getIsLastColumn("left")
  const isFirstRightPinnedColumn =
    isPinned === "right" && column.getIsFirstColumn("right")

  return {
    boxShadow: withBorder
      ? isLastLeftPinnedColumn
        ? "-4px 0 4px -4px hsl(var(--border)) inset"
        : isFirstRightPinnedColumn
          ? "4px 0 4px -4px hsl(var(--border)) inset"
          : undefined
      : undefined,
    left: isPinned === "left" ? `${column.getStart("left")}px` : undefined,
    right: isPinned === "right" ? `${column.getAfter("right")}px` : undefined,
    opacity: isPinned ? 0.97 : 1,
    position: isPinned ? "sticky" : "relative",
    background: isPinned ? "hsl(var(--background))" : "hsl(var(--background))",
    width: column.getSize(),
    zIndex: isPinned ? 1 : 0,
  }
}

export function getFilterOperators(filterVariant: FilterVariant) {
  const operatorMap: Record<
    FilterVariant,
    { label: string; value: FilterOperator }[]
  > = {
    text: dataTableConfig.textOperators,
    number: dataTableConfig.numericOperators,
    range: dataTableConfig.numericOperators,
    date: dataTableConfig.dateOperators,
    dateRange: dataTableConfig.dateOperators,
    boolean: dataTableConfig.booleanOperators,
    select: dataTableConfig.selectOperators,
    multiSelect: dataTableConfig.multiSelectOperators,
  }

  return operatorMap[filterVariant] ?? dataTableConfig.textOperators
}

export function getDefaultFilterOperator(filterVariant: FilterVariant) {
  const operators = getFilterOperators(filterVariant)

  return operators[0]?.value ?? (filterVariant === "text" ? "iLike" : "eq")
}

export function getValidFilters<TData>(
  filters: ExtendedColumnFilter<TData>[]
): ExtendedColumnFilter<TData>[] {
  return filters.filter(
    (filter) =>
      filter.operator === "isEmpty" ||
      filter.operator === "isNotEmpty" ||
      (Array.isArray(filter.value)
        ? filter.value.length > 0
        : filter.value !== "" &&
          filter.value !== null &&
          filter.value !== undefined)
  )
}

/**
 * Construct Prisma conditions based on the provided filters for a specific table.
 *
 * This function takes a table and an array of filters, and returns a Prisma
 * query object that represents the logical combination of these conditions. The conditions
 * are combined using the specified join operator (either 'AND' or 'OR'), which is determined
 * by the first filter's joinOperator property.
 *
 * Each filter can specify various operators (e.g., equality, inequality,
 * comparison for numbers and dates, etc.) and the function will generate the appropriate
 * Prisma query objects based on the filter's type and value.
 *
 * @param filters - An array of filters to be applied to the table.
 * @param joinOperator - The join operator to use for combining the filters.
 * @returns A Prisma query object representing the combined filters, or undefined if no valid
 * filters are found.
 */

export function filterColumns({
  filters,
  joinOperator,
}: {
  filters: FilterItemSchema[]
  joinOperator: JoinOperator
}) {
  const conditions = filters.map((filter) => {
    const column = filter.id

    switch (filter.operator) {
      case "eq":
        if (Array.isArray(filter.value)) {
          return { [column]: { in: filter.value } }
        } else if (
          filter.variant === "boolean" &&
          typeof filter.value === "string"
        ) {
          return { [column]: filter.value === "true" }
        } else if (filter.variant === "date") {
          const date = new Date(filter.value)
          const start = startOfDay(date)
          const end = endOfDay(date)
          return { [column]: { gte: start, lte: end } }
        } else {
          return { [column]: filter.value }
        }
      case "ne":
        if (Array.isArray(filter.value)) {
          return { [column]: { notIn: filter.value } }
        } else if (filter.variant === "boolean") {
          return { [column]: { not: filter.value === "true" } }
        } else if (filter.variant === "date") {
          const date = new Date(filter.value)
          const start = startOfDay(date)
          const end = endOfDay(date)
          return {
            OR: [{ [column]: { lt: start } }, { [column]: { gt: end } }],
          }
        } else {
          return { [column]: { not: filter.value } }
        }
      case "iLike":
        return filter.variant === "text" && typeof filter.value === "string"
          ? { [column]: { contains: filter.value, mode: "insensitive" } }
          : undefined
      case "notILike":
        return filter.variant === "text" && typeof filter.value === "string"
          ? {
              [column]: {
                not: { contains: filter.value, mode: "insensitive" },
              },
            }
          : undefined
      case "lt":
        return filter.variant === "number"
          ? { [column]: { lt: filter.value } }
          : filter.variant === "date" && typeof filter.value === "string"
            ? { [column]: { lt: endOfDay(new Date(Number(filter.value))) } }
            : undefined
      case "lte":
        return filter.variant === "number"
          ? { [column]: { lte: filter.value } }
          : filter.variant === "date" && typeof filter.value === "string"
            ? { [column]: { lte: endOfDay(new Date(filter.value)) } }
            : undefined
      case "gt":
        return filter.variant === "number"
          ? { [column]: { gt: filter.value } }
          : filter.variant === "date" && typeof filter.value === "string"
            ? { [column]: { gt: startOfDay(new Date(Number(filter.value))) } }
            : undefined
      case "gte":
        return filter.variant === "number"
          ? { [column]: { gte: filter.value } }
          : filter.variant === "date" && typeof filter.value === "string"
            ? { [column]: { gte: startOfDay(new Date(filter.value)) } }
            : undefined
      case "isBetween":
        return filter.variant === "date" &&
          Array.isArray(filter.value) &&
          filter.value.length === 2
          ? {
              AND: [
                filter.value[0]
                  ? {
                      [column]: {
                        gte: startOfDay(new Date(Number(filter.value[0]))),
                      },
                    }
                  : undefined,
                filter.value[1]
                  ? {
                      [column]: {
                        lte: endOfDay(new Date(Number(filter.value[1]))),
                      },
                    }
                  : undefined,
              ].filter(Boolean),
            }
          : undefined
      case "isRelativeToToday":
        if (filter.variant === "date" && typeof filter.value === "string") {
          const today = new Date()
          const [amount, unit] = filter.value.split(" ") ?? []
          let startDate: Date
          let endDate: Date

          if (!amount || !unit) return undefined

          switch (unit) {
            case "days":
              startDate = startOfDay(addDays(today, parseInt(amount)))
              endDate = endOfDay(startDate)
              break
            case "weeks":
              startDate = startOfDay(addDays(today, parseInt(amount) * 7))
              endDate = endOfDay(addDays(startDate, 6))
              break
            case "months":
              startDate = startOfDay(addDays(today, parseInt(amount) * 30))
              endDate = endOfDay(addDays(startDate, 29))
              break
            default:
              return undefined
          }

          return {
            AND: [
              { [column]: { gte: startDate } },
              { [column]: { lte: endDate } },
            ],
          }
        }
        return undefined
      case "isEmpty":
        return { [column]: null }
      case "isNotEmpty":
        return { NOT: { [column]: null } }
      default:
        throw new Error(`Unsupported operator: ${filter.operator}`)
    }
  })

  const validConditions = conditions.filter(
    (condition) => condition !== undefined
  )

  // eslint-disable-next-line
  //@ts-ignore
  return validConditions.length > 0
    ? { [joinOperator]: validConditions }
    : undefined
}
