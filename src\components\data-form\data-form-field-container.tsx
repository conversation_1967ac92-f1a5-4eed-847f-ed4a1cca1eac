import React from "react"

import { Variants } from "@/types/data-form"
import { dataFormConfig } from "@/config/data-form"
import { cn } from "@/lib/utils"

export default function DataFormFieldContainer({
  children,
  variant,
  autoInline,
  className,
}: {
  children: React.ReactNode
  variant: Variants
  autoInline?: boolean
  className?: string
}) {
  // الحقول التي يمكن عرضها على شكل حقلين في سطر واحد عندما تكون المساحة واسعة
  // ستكون الحقول مرنه بمعنى انه عندما يكون هناك عنصر من fieldsAllowInline وبعده او قبلة عنصر من fieldsAllowInline سوف يتم عرض العنصرين في سطر واحد
  const isAllowedInline = dataFormConfig.fieldsAllowInline.includes(variant)

  const isAutoInline = isAllowedInline && autoInline

  return (
    <div
      className={cn(
        isAutoInline ? "grow basis-[45%] @max-2xl:basis-full" : "w-full",
        className
      )}
    >
      {children}
    </div>
  )
}
