import * as React from "react"
import { useFormContext } from "react-hook-form"

import { DataFormFieldProps } from "@/types/data-form"

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form"
import { Textarea } from "../ui/textarea"
import DataFormFieldContainer from "./data-form-field-container"

interface DataFormFieldTextareaProps<T = string> extends DataFormFieldProps<T> {
  /**
   * عندما يكون صحيح, يتم توسيع الحقل بشكل تلقائي بحجم النص الذي بداخلة
   */
  autoResize?: boolean
}

const DataFormFieldTextarea = (props: DataFormFieldTextareaProps) => {
  const { control } = useFormContext()

  const {
    label,
    name,
    autoResize,
    description,
    disabled,
    placeholder,
    required,
  } = props

  return (
    <FormField
      key={name}
      name={name}
      control={control}
      render={({ field }) => (
        <DataFormFieldContainer variant="textarea">
          <FormItem className="col-span-2">
            <FormLabel>
              {label}
              {required && <span className="text-destructive"> *</span>}
            </FormLabel>
            <FormControl>
              <Textarea
                {...field}
                value={field.value ?? ""}
                required={required}
                autoResize={autoResize}
                placeholder={placeholder}
                disabled={disabled ?? field.disabled}
              />
            </FormControl>
            <FormDescription>{description}</FormDescription>
            <FormMessage />
          </FormItem>
        </DataFormFieldContainer>
      )}
    />
  )
}

export default DataFormFieldTextarea
export type { DataFormFieldTextareaProps }
