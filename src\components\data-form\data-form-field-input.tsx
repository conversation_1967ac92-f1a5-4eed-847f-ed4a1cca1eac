import * as React from "react"
import { useFormContext } from "react-hook-form"

import { DataFormFieldProps } from "@/types/data-form"

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form"
import { Input } from "../ui/input"
import DataFormFieldContainer from "./data-form-field-container"

interface DataFormFieldInputProps<T = string> extends DataFormFieldProps<T> {
  type?: "text" | "email" | "password" | "url" | "search"
  autoComplete?: React.HTMLInputAutoCompleteAttribute
  /**
   * عندما يكون صحيح, يتم عرض الحقل بشكل مرن.
   * بمعنى انه سيتم التحقق مما اذا كان هناك حقل قبل او بعد هذا العنصر إذا وجد الحقل سيتم عرض الحقلين في صف واحد
   * ملاحضة: سيتم عرض كل حقل في صف منفصل عندما تكون المساحة اقل من () حتا اذا كانت هذا الخاصية صحيحة وهذا يضمن عرض الحقول بشكل غير مزدحم في المساحات والشاشات الصغيرة
   * @default true
   */
  autoInline?: boolean
}

const DataFormFieldInput = (props: DataFormFieldInputProps) => {
  const { control } = useFormContext()

  const {
    label,
    name,
    autoComplete,
    description,
    disabled,
    placeholder,
    required,
    type = "text",
    autoInline = true,
  } = props

  return (
    <FormField
      key={name}
      name={name}
      control={control}
      render={({ field }) => (
        <DataFormFieldContainer variant="input" autoInline={autoInline}>
          <FormItem className="grow basis-[45%] @max-xl:basis-full">
            <FormLabel>
              {label}
              {required && <span className="text-destructive"> *</span>}
            </FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value ?? ""}
                type={type}
                required={required}
                placeholder={placeholder}
                autoComplete={autoComplete}
                disabled={disabled ?? field.disabled}
              />
            </FormControl>
            <FormDescription>{description}</FormDescription>
            <FormMessage />
          </FormItem>
        </DataFormFieldContainer>
      )}
    />
  )
}

export default DataFormFieldInput
export type { DataFormFieldInputProps }
