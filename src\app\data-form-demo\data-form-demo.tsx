// DataFormDemo.tsx

"use client"

import React from "react"
import { Admin } from "@/db/generated"
import { z } from "zod"

import { DataFormField, DataFormSubmitHandler } from "@/types/data-form"
import { DataForm } from "@/components/data-form/data-form"

const schema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email"),
  profileImageUrl: z.any().optional(),
  isActive: z.boolean().refine((check) => check === true),
})

export default function DataFormDemo() {
  const fields: DataFormField<Admin>[] = [
    {
      name: "name",
      label: "Name",
      variant: "input",
      placeholder: "Enter title...",
      description:
        "ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ddddd ",
    },
    {
      name: "email",
      label: "Email",
      variant: "input",
      type: "email",
    },
    {
      name: "role",
      label: "Role",
      variant: "select",
      options: [
        { value: "ADMIN", label: "Admin" },
        { value: "MODERATOR", label: "Moderator" },
        { value: "SUPER_ADMIN", label: "Super admin" },
        { value: "STAFF", label: "Staff" },
      ],
    },
    {
      name: "profileImageUrl",
      label: "Avatar",
      variant: "fileUpload",
      accept: ["image/*"],
    },
    {
      variant: "tagsInput",
      name: "blockedPaths",
      label: "Blocked paths",
      editable: true,
      placeholder: "Enter blocked paths...",
    },
    {
      name: "isActive",
      label: "Status",
      variant: "checkbox",
      description: "checkbox checkbox checkbox",
    },
  ]

  const onSubmit: DataFormSubmitHandler<Admin> = async (values) => {
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const { success, data, error } = schema.safeParse(values)

    if (!success) {
      return {
        success: false,
        errors: error.flatten().fieldErrors,
        message: "خطأ في إرسال النموذج",
      }
    }

    console.log(data)
    return { success: true, message: "تم إرسال النموذج بنجاح" }
  }

  return (
    <DataForm
      schema={schema}
      mode="create"
      fields={fields}
      onSubmit={onSubmit}
    />
  )
}
