// DataFormButtons.tsx

import React from "react"
import { useFormContext, useFormState } from "react-hook-form"

import {
  DataFormProps,
  DataFormPropsOptions,
  DataFormValues,
} from "@/types/data-form"
import { Button } from "@/components/ui/button"

interface DataFormButtonsProps<TData extends DataFormValues> {
  options?: DataFormPropsOptions
  mode: DataFormProps<TData>["mode"]
}

export function DataFormButtons<TData extends DataFormValues>({
  options,
  mode,
}: DataFormButtonsProps<TData>) {
  const { reset } = useFormContext()
  const { isValid, isDirty, isSubmitting } = useFormState()

  // في وضع الإنشاء يجب أن يكون زر الإرسال مُعطلًا إذا كان احد الحقول مخالف للشروط الـ schema
  // في وضع التعديل يجب أن يكون زر الإرسال مُعطلًا إذا لم يتم تغيير أي حقل
  const isDisabledSubmitButton = isSubmitting || !isValid || !isDirty

  return (
    <div className="col-span-2 flex w-full items-center gap-2">
      <Button
        type="submit"
        disabled={isDisabledSubmitButton}
        className={options?.submitButton?.className}
      >
        {(options?.submitButton?.label ?? mode === "create")
          ? "Create"
          : "Update"}
      </Button>
      <Button
        type="button"
        variant="outline"
        className={options?.resetButton?.className}
        onClick={() => {
          reset()
          options?.resetButton?.onClick?.()
        }}
        disabled={isSubmitting}
      >
        {options?.resetButton?.label ?? "Reset"}
      </Button>
    </div>
  )
}
