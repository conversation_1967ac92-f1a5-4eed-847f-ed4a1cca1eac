import Link from "next/link"

import "./globals.css"

import { NuqsAdapter } from "nuqs/adapters/next/app"

import { Toaster } from "@/components/ui/sonner"

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className="h-full">
      <body className={`dark min-h-full w-full antialiased`}>
        <NuqsAdapter>
          <div className="flex gap-4">
            <Link href="/data-table-demo">Data Table Demo</Link>
            <Link href="/data-form-demo">Data Form Demo</Link>
          </div>
          <main className="px-2 py-20 md:px-6">{children}</main>
        </NuqsAdapter>
        <Toaster richColors />
      </body>
    </html>
  )
}
