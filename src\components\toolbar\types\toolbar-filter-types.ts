import React from "react"
import { z } from "zod/v4"

import { DynamicObject } from "@/types/globals"

import type { filterJoinStateSchema, filterStateSchema } from "../lib"

export type FilterVariant =
  | "text"
  | "number"
  | "date"
  | "boolean"
  | "select"
  | "multiSelect"

  // It is used internally only and is not defined by the ToolbarFilter items properties.
  | "dateRange"
  | "numberRange"

export type Operator = z.infer<typeof filterStateSchema>["operator"]
export type FilterState = z.infer<typeof filterStateSchema>
export type FilterJoinState = z.infer<typeof filterJoinStateSchema>

export interface BaseFilterItem<K extends string = string> {
  /**
   * Unique identifier for the filter field.
   * Must match a property key in the data object being filtered.
   */
  id: K

  /**
   * Display label shown to users in the filter interface.
   */
  label: string

  /**
   * Placeholder text shown in empty input fields.
   */
  placeholder?: string
}

export interface TextFilterItem<K extends string = string>
  extends BaseFilterItem<K> {
  /**
   * Type of filter to render and validation to apply.
   * 'text': String-based filtering with operators like "contains", "equals"
   */
  variant: "text"
}

export interface NumberFilterItem<K extends string = string>
  extends BaseFilterItem<K> {
  /**
   * Type of filter to render and validation to apply.
   * 'number': Numeric filtering with operators like "greater than", "less than"
   */
  variant: "number"

  /**
   * Value range constraints.
   * Defines minimum and maximum allowed values for input.
   */
  range?: { min?: number; max?: number }

  /**
   * Step increment for number input controls.
   */
  step?: number

  /**
   * Unit of measurement displayed next to the input field.
   * Example: '$' for currency, 'kg' for weight, etc.
   */
  unit?: string
}

export interface DateFilterItem<K extends string = string>
  extends BaseFilterItem<K> {
  /**
   * Type of filter to render and validation to apply.
   * 'date': Date-based filtering with date range support
   */
  variant: "date"

  /**
   * Date range constraints in ISO 8601 format.
   * Defines minimum and maximum allowed dates for input.
   *
   * @see https://ar.wikipedia.org/wiki/ISO_8601
   */
  range?: { min?: Date | string; max?: Date | string }
}

export interface BooleanFilterItem<K extends string = string>
  extends BaseFilterItem<K> {
  /**
   * Type of filter to render and validation to apply.
   * 'boolean': True/false filtering
   */
  variant: "boolean"
}

export interface SelectFilterItem<K extends string = string, V = unknown>
  extends BaseFilterItem<K> {
  /**
   * Type of filter to render and validation to apply.
   * 'select': Single selection from predefined options
   * 'multiSelect': Multiple selection from predefined options
   */
  variant: "select" | "multiSelect"

  /**
   * Available options for selection.
   */
  options: Array<{
    /**
     * Display text shown to users for this option.
     */
    label: string

    /**
     * Actual value used for filtering operations.
     */
    value: V

    /**
     * Optional count of items associated with this option.
     * Displayed next to the option label in the interface.
     */
    count?: number

    /**
     * Optional React component rendered as an icon next to the option.
     * Component must accept a className prop.
     */
    icon?: React.ComponentType<{ className?: string; [key: string]: any }>
  }>
}

export type FilterItem<T extends DynamicObject = DynamicObject> = {
  [K in keyof T]:
    | TextFilterItem<Extract<K, string>>
    | NumberFilterItem<Extract<K, string>>
    | DateFilterItem<Extract<K, string>>
    | BooleanFilterItem<Extract<K, string>>
    | SelectFilterItem<Extract<K, string>, T[K]>
}[keyof T]

export type ToolbarFilterProps<T extends DynamicObject = DynamicObject> = {
  /**
   * قائمة بيانات العناصر التي سيتم استخداها للتصفية.
   */
  items: FilterItem<T>[]
  /**
   * إذا كانت القيمة true، سيتم تحديث عنوان الرابط (URL) باستخدام التصفية
   * دون إعادة تحميل الصفحة (shallow routing).
   *
   * @default false
   */
  shallow?: boolean
  /**
   * مدة التأخير (بالملي ثانية) قبل تطبيق الفلتر وتحديث القيمة في عنوان الرابط (URL).
   * يُستخدم هذا لتقليل عدد مرات التحديث عند الكتابة أو التفاعل مع الفلتر.
   *
   * @default 300
   */
  debounceMs?: number
  /**
   * يتم تطبيق الخصائص على عنصر الزر الخاص بفتح نافذة القائمة فقط.
   */
  className?: string
}

export interface FilterContextValue {
  items: FilterItem[]
  shallow: boolean
  debounceMs: number
}
