import * as React from "react"

import { cn } from "@/lib/utils"

type TextareaProps = React.ComponentProps<"textarea"> & {
  /** If true, the textarea will automatically resize its height based on the content. */
  autoResize?: boolean
}

function Textarea({ className, autoResize, ...props }: TextareaProps) {
  const textareaRef = React.useRef<HTMLTextAreaElement>(null)

  React.useEffect(() => {
    if (!autoResize || !textareaRef.current) return
    const textarea = textareaRef.current
    const resize = () => {
      textarea.style.height = "auto"
      textarea.style.height = textarea.scrollHeight + "px"
    }
    resize()
    textarea.addEventListener("input", resize)
    return () => textarea.removeEventListener("input", resize)
  }, [autoResize, props.value])

  return (
    <textarea
      ref={textareaRef}
      style={{ maxHeight: 400 }}
      data-slot="textarea"
      className={cn(
        "border-input selection:bg-primary selection:text-primary-foreground placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        className
      )}
      {...props}
    />
  )
}

export { Textarea }
