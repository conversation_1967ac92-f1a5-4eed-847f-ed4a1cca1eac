"use client"

import * as React from "react"
import dynamic from "next/dynamic"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { toast } from "sonner"

import { DataFormProps, DataFormValues } from "@/types/data-form"
import { setServerErrors } from "@/lib/data-form"
import { cn } from "@/lib/utils"
import { Form } from "@/components/ui/form"

import { DataFormButtons } from "./data-form-buttons"
import DataFormFieldCheckbox from "./data-form-field-checkbox"
import { DataFormFieldFileUploadSkeleton } from "./data-form-field-file-upload"
import DataFormFieldInput from "./data-form-field-input"
import DataFormFieldPhoneInput from "./data-form-field-phone-input"
import DataFormFieldSelect from "./data-form-field-select"
import { DataFormFieldTagsInputSkeleton } from "./data-form-field-tags-input"
import DataFormFieldTextarea from "./data-form-field-textarea"

const DataFormFieldFileUpload = dynamic(
  () => import("./data-form-field-file-upload").then((e) => e.default),
  { ssr: false, loading: () => <DataFormFieldFileUploadSkeleton /> }
)

const DataFormFieldTagsInput = dynamic(
  () => import("./data-form-field-tags-input").then((e) => e.default),
  { ssr: false, loading: () => <DataFormFieldTagsInputSkeleton /> }
)

export function DataForm<TData extends DataFormValues>({
  mode,
  schema,
  options,
  className,
  defaultValues,
  fields,
  onSubmitAction,
}: DataFormProps<TData>) {
  //
  const { reset, setError, getValues, ...form } = useForm<TData>({
    defaultValues,
    resolver: zodResolver(schema),
    disabled: options?.disableAllFields,
    mode: options?.validationMode ?? "onSubmit",
    reValidateMode: options?.reValidateMode ?? "onChange",
  })

  const handleReset = React.useCallback(() => {
    reset(mode === "create" ? defaultValues : getValues())
  }, [reset, mode, getValues, defaultValues])

  // دالة معالجة تقديم النموذج
  const handleSubmit = async (values: TData) => {
    const { success, errors, message } = await onSubmitAction?.(values)

    if (!success) {
      setServerErrors(setError, errors)
      if (message) toast.error(message)
    } else {
      toast.success("Form submitted successfully")
      handleReset()
    }
  }

  return (
    <Form {...form} reset={reset} setError={setError} getValues={getValues}>
      <form
        className={cn("@container flex flex-wrap items-start gap-8", className)}
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        {React.useMemo(() => {
          return fields?.map((field) => {
            switch (field.variant) {
              case "input":
                return <DataFormFieldInput {...field} key={field.name} />

              case "textarea":
                return <DataFormFieldTextarea {...field} key={field.name} />

              case "select":
                return <DataFormFieldSelect {...field} key={field.name} />

              case "fileUpload":
                return <DataFormFieldFileUpload {...field} key={field.name} />

              case "tagsInput":
                return <DataFormFieldTagsInput {...field} key={field.name} />

              case "phoneInput":
                return <DataFormFieldPhoneInput {...field} key={field.name} />

              case "checkbox":
                return <DataFormFieldCheckbox {...field} key={field.name} />
            }
          })
        }, [fields])}
        <DataFormButtons options={options} mode={mode} />
      </form>
    </Form>
  )
}
