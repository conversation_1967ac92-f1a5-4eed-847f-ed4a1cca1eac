import * as React from "react"
import { useFormContext } from "react-hook-form"
import { Country } from "react-phone-number-input"

import { DataFormFieldProps } from "@/types/data-form"

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form"
import { PhoneInput } from "../ui/phone-input"
import DataFormFieldContainer from "./data-form-field-container"

interface DataFormFieldPhoneInputProps<T = string>
  extends DataFormFieldProps<T> {
  defaultCountry?: Country
  /**
   * عندما يكون صحيح, يتم عرض الحقل بشكل مرن.
   * بمعنى انه سيتم التحقق مما اذا كان هناك حقل قبل او بعد هذا العنصر إذا وجد الحقل سيتم عرض الحقلين في صف واحد
   * ملاحضة: سيتم عرض كل حقل في صف منفصل عندما تكون المساحة اقل من () حتا اذا كانت هذا الخاصية صحيحة وهذا يضمن عرض الحقول بشكل غير مزدحم في المساحات والشاشات الصغيرة
   * @default true
   */
  autoInline?: boolean
}

const DataFormFieldPhoneInput = (props: DataFormFieldPhoneInputProps) => {
  const { control } = useFormContext()

  const {
    label,
    name,
    description,
    disabled,
    placeholder,
    required,
    defaultCountry,
    autoInline = true,
  } = props

  return (
    <FormField
      key={name}
      name={name}
      control={control}
      render={({ field }) => (
        <DataFormFieldContainer variant="phoneInput" autoInline={autoInline}>
          <FormItem>
            <FormLabel>
              {label}
              {required && <span className="text-destructive"> *</span>}
            </FormLabel>
            <FormControl>
              <PhoneInput
                {...field}
                disabled={disabled ?? field.disabled}
                defaultCountry={defaultCountry}
                placeholder={placeholder}
                required={required}
              />
            </FormControl>
            <FormDescription>{description}</FormDescription>
            <FormMessage />
          </FormItem>
        </DataFormFieldContainer>
      )}
    />
  )
}

export default DataFormFieldPhoneInput
export type { DataFormFieldPhoneInputProps }
