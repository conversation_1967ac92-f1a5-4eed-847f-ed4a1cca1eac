"use client"

import "filepond/dist/filepond.min.css"

import * as React from "react"
import dynamic from "next/dynamic"
import { FilePondFile, FilePondInitialFile, registerPlugin } from "filepond"
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type"
import filepondLocal_ar from "filepond/locale/ar-ar.js"
import { FilePondProps } from "react-filepond"

import { getFileInfoFromUrl } from "@/lib/get-file-info-from-url"

import { cn } from "../../lib/utils"
import { Skeleton } from "./skeleton"

const FilePond = dynamic(
  () => import("react-filepond").then((e) => e.FilePond),
  {
    ssr: false,
    loading: () => <Skeleton className="h-19 w-full" />,
  }
) as React.ComponentClass<FilePondProps>

interface FileUploadProps {
  id?: string
  name: string
  value: string[]
  accept?: string[]
  maxFiles?: number
  multiple?: boolean
  allowReorder?: boolean
  placeholder?: string
  disabled?: boolean
  onChange: (filesUrls: string[]) => void
}

registerPlugin(FilePondPluginFileValidateType)
export default function FileUpload(props: FileUploadProps) {
  const {
    id,
    name,
    value,
    disabled,
    maxFiles = 5,
    multiple = false,
    allowReorder = false,
    accept = ["image/*"],
    onChange,
  } = props

  // يمكننا المتغير canSetInitialValues من تعيين القيم الأولية مرة واحدة فقط
  // هذا مهم لأننا لا نريد إعادة تعيين القيم الأولية في كل مرة يتم فيها إعادة عرض المكون
  const canSetInitialValues = React.useRef<boolean>(true)
  const [files, setFiles] = React.useState<
    (string | FilePondInitialFile | Blob | FilePondFile)[]
  >([])

  React.useEffect(() => {
    if (canSetInitialValues.current === true && value.length > 0) {
      // يتم تعيينها الى false بعد تعيين القيم الأولية وهذا يضمن انه لن تعمل الدالة مع الملفات التي يتم رفعا بعد ذلك
      // هذا مهم لأننا لا نريد إعادة تعيين القيم الأولية في كل مرة يتم فيها إعادة عرض المكون
      // هذا يعني اننا لا نريد تعيين القيم الأولية مرة أخرى
      canSetInitialValues.current = false

      // الحصول على معلومات الملفات من الروابط عن طريق طلبات HEAD
      const filePondInitialFile = value
        .filter((v) => v !== "")
        .map(async (fileUrl): Promise<FilePondInitialFile> => {
          const fileInfo = await getFileInfoFromUrl(fileUrl)

          return {
            source: fileUrl,
            options: {
              type: "limbo",
              file: {
                name: fileInfo.fileName,
                size: fileInfo.size,
                type: fileInfo.type,
              },
            },
          }
        })

      Promise.all(filePondInitialFile).then((files) => {
        setFiles(files)
      })
    }
  }, [value])
  // console.log(ar_ar)

  return React.useMemo(
    () => (
      <div className="relative min-h-20">
        <FilePond
          id={id}
          disabled={disabled}
          name={name}
          maxFiles={maxFiles}
          allowMultiple={multiple}
          allowReorder={multiple && allowReorder}
          acceptedFileTypes={accept}
          files={files as File[]}
          onupdatefiles={setFiles}
          {...filepondLocal_ar}
          onreorderfiles={(files) => {
            onChange(files.map((f) => f.serverId))
          }}
          credits={false}
          server={{
            process: {
              url: "/api/upload",
              method: "POST",
              onload: (response) => {
                // هنا يمكنك معالجة الاستجابة من الخادم
                // على سبيل المثال، إذا كان الخادم يعيد رابط الملف، يمكنك تحديث الحالة هنا
                const fileUrl = JSON.parse(response).fileUrl
                onChange([fileUrl, ...value])
                return fileUrl
              },
            },
            // هذه الدالة (revert) تمكننا من حذف الملفات المحملة من الخادم
            // ولكن ماذا لو كان هذا الملف مطلوب وكانت هناك بيانات في قاعدة البيانات تشير إلى هذا الملف؟
            // ويقوم المستخدم بحذف الملف والخروج من الصفحة بدون ان يقوم برفع ملفات بديله للتي تم حذفها
            // لذلك لتجنب هذه المشكلة سنقوم بعمل التالي:
            // 1- عند حذف الملف سوف نتأكد مما اذا كان الملف ضمن البيانات الأولية (الإفتراضية) اذا كان كذلك سنقوم بحذف الملف من value وعدم حذفه من السيرفر
            // وعند ارسال النموذج سنقوم بحفظ الملف الجديد والتأكد من نجاح الحفظ ثم نقوم بحذف الملف الذي تم حذفة من قيمة value
            // 2- يمكن للمستخدم حذف الملفات التي قام برفعة في هذه الجلسة من السيرفر وكذلك من قيمة value وهذا آمن لأن الملف غير مرتبط بأي بيانات
            revert: async (source: string, load) => {
              onChange(value.filter((f) => f !== source))
              load()
            },
            // لا نحتاج إلى تحميل الملفات على الإطلاق لسبب استنزاف بيانات الإنترنت عند التعامل مع الملفات الكبيرة
            // وبدلا من ذلك نقوم بجلب معلومات الملف من خطاف useEffect عن طريق طلب HEAD فقط
            load: null,
            fetch: null,
            restore: null, // لا نحتاج إلى جلب الملفات في هذا المثال
          }}
          className={cn([
            "mb-0!",
            // الخلفية
            "[&_.filepond--panel-root]:bg-background! [&_.filepond--panel-root]:dark:bg-[#0E121C]!",
            // الإطار
            "[&_.filepond--panel-root]:border-border! [&_.filepond--panel-root]:border [&_.filepond--panel-root]:border-dashed",
            // خلفية الملف
            "[&_.filepond--item-panel]:bg-muted!",
            // بيانات الملف
            "[&_.filepond--file]:text-foreground!",
            // label
            "[&_.filepond--drop-label]:text-muted-foreground!",
            // نصوص حالة الملف
            "[&_.filepond--file-status]:*:[direction:rtl]! [&_.filepond--file-status]:*:first:mb-1!",
            // نصوص معلومات الملف
            "[&_.filepond--file-info]:*:first:mb-1!",
            // دائرة الإسقاط
            "[&_.filepond--drip-blob]:bg-muted! [&_.filepond--drip]:opacity-100!",
            // لون أيقونات أزرار الإجراءات السوداء
            "[&_.filepond--file-action-button]:text-white!",
            // الزر
            "[&_.filepond--file-action-button]:cursor-pointer!",
          ])}
        />
      </div>
    ),
    [
      id,
      disabled,
      name,
      maxFiles,
      multiple,
      allowReorder,
      accept,
      files,
      onChange,
      value,
    ]
  )
}
