import { FieldPath, UseFormReturn } from "react-hook-form"

import {
  // DataFormField,
  DataFormSubmitResponse,
  DataFormValues,
} from "@/types/data-form"

// import { dataFormConfig } from "@/config/data-form"

/**
 * توليد القيم الافتراضية لحقل النموذج حتا اذا لم يتم تعيينها في قيم حقول النموذج بشكل صريح
 * @param fields حقول النموذج
 * @param defaultValues كائن يأخذ القيم الافتراضية لكل حقول النموذج
 * @returns {initialValues, defaultValues}
 */
// const generateDefaultValues = <TData extends DataFormValues>(
//   fields: DataFormField<TData>[],
//   initialValues: Partial<TData> = {}
// ) => {
//   const extractedDefaultValues = fields.reduce((acc, field) => {
//     acc[field.name] = dataFormConfig.defaultValues[field.variant]
//     return acc
//   }, {} as any)

//   return {
//     initialValues: {
//       ...extractedDefaultValues,
//       ...initialValues,
//     } as DefaultValues<TData>,
//   }
// }

const setServerErrors = <TData extends DataFormValues>(
  setError: UseFormReturn<TData, any, TData>["setError"],
  errors?: DataFormSubmitResponse<TData>["errors"]
) => {
  if (!errors) return

  for (const key in errors) {
    const field = key as FieldPath<TData>
    const messages = errors[field]

    if (messages && messages.length > 0) {
      setError(field, {
        type: "server",
        message: messages[0], // عرض أول رسالة فقط
      })
    }
  }
}

export { setServerErrors }
