{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "toolbar", "type": "registry:block", "title": "<PERSON><PERSON><PERSON>", "description": "A toolbar component for filtering, searching, and sorting data", "files": [{"path": "registry/new-york/blocks/toolbar/toolbar.tsx", "content": "import * as React from \"react\"\r\n\r\nimport { ToolbarContext } from \"./contexts\"\r\nimport { getToolbarDictionary } from \"./lib\"\r\nimport { ToolbarProps } from \"./types\"\r\n\r\nexport function Toolbar({\r\n  children,\r\n  className,\r\n  locale = \"ar\",\r\n  dir = \"rtl\",\r\n  startTransition,\r\n}: ToolbarProps) {\r\n  const dictionaries = React.useMemo(\r\n    () => getToolbarDictionary(locale),\r\n    [locale]\r\n  )\r\n\r\n  return (\r\n    <ToolbarContext value={{ startTransition, dictionaries, dir }}>\r\n      <div className={className} dir={dir}>\r\n        {children}\r\n      </div>\r\n    </ToolbarContext>\r\n  )\r\n}\r\n\r\nexport { getToolbarQueryParser } from \"./lib/toolbar-lib\"\r\n", "type": "registry:component", "target": "./toolbar/toolbar.tsx"}, {"path": "registry/new-york/blocks/toolbar/toolbar-filter.tsx", "content": "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport dynamic from \"next/dynamic\"\r\nimport { formatDate } from \"date-fns\"\r\nimport {\r\n  CheckIcon,\r\n  ChevronDown,\r\n  ChevronsUpDownIcon,\r\n  GripVertical,\r\n  ListFilter,\r\n  SearchIcon,\r\n  Trash2,\r\n} from \"lucide-react\"\r\nimport { ar } from \"react-day-picker/locale\"\r\nimport { toast } from \"sonner\"\r\n\r\nimport { DynamicObject } from \"@/types/globals\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Calendar } from \"@/components/ui/calendar\"\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\"\r\nimport {\r\n  Faceted,\r\n  FacetedBadgeList,\r\n  FacetedContent,\r\n  FacetedEmpty,\r\n  FacetedGroup,\r\n  FacetedInput,\r\n  FacetedItem,\r\n  FacetedList,\r\n  FacetedTrigger,\r\n} from \"@/components/ui/faceted\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\"\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Sortable,\r\n  SortableContent,\r\n  SortableItem,\r\n  SortableItemHandle,\r\n} from \"@/components/ui/sortable\"\r\n\r\nimport { FilterContext } from \"./contexts\"\r\nimport { useFilterState } from \"./hooks\"\r\nimport { getDefaultFilterOperator, getFilterOperators } from \"./lib\"\r\nimport {\r\n  FilterItem,\r\n  FilterJoinState,\r\n  FilterState,\r\n  Operator,\r\n  ToolbarFilterProps,\r\n} from \"./types\"\r\n\r\nexport function ToolbarFilter<T extends DynamicObject = DynamicObject>({\r\n  items,\r\n  shallow = false,\r\n  debounceMs = 300,\r\n  className,\r\n}: ToolbarFilterProps<T>) {\r\n  return (\r\n    <FilterContext\r\n      value={{\r\n        items,\r\n        shallow,\r\n        debounceMs,\r\n      }}\r\n    >\r\n      <Filter className={className} />\r\n    </FilterContext>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// المكون الرئيسي الذي يحتوي على جميع مكونات وعناصر الفلترات\r\n// تم فصلة الى مكون منفصل لكي يتمكن من استخدام خطاف الفلتر\r\nfunction Filter({ className }: { className?: string }) {\r\n  const id = React.useId()\r\n  const labelId = React.useId()\r\n  const descriptionId = React.useId()\r\n  const { filters, setFilters, addFilter, resetFilters, dictionaries, dir } =\r\n    useFilterState()\r\n\r\n  return (\r\n    <Sortable\r\n      value={filters}\r\n      onValueChange={setFilters}\r\n      getItemValue={(item: FilterState) => item.filterId}\r\n    >\r\n      <Popover>\r\n        <PopoverTrigger asChild>\r\n          <Button variant=\"outline\" size=\"sm\" className={className}>\r\n            <ListFilter />\r\n            {dictionaries.triggerButtonLabel}\r\n            {filters.length > 0 && (\r\n              <Badge\r\n                variant=\"secondary\"\r\n                className=\"h-[18.24px] rounded-[3.2px] px-[5.12px] font-mono text-[10.4px] font-normal\"\r\n              >\r\n                {filters.length}\r\n              </Badge>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent\r\n          dir={dir}\r\n          align=\"start\"\r\n          aria-describedby={descriptionId}\r\n          aria-labelledby={labelId}\r\n          className=\"flex w-full max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-3.5 p-4 sm:min-w-[380px]\"\r\n        >\r\n          <div className=\"flex flex-col gap-1\">\r\n            <h4 id={labelId} className=\"leading-none font-medium\">\r\n              {filters.length > 0\r\n                ? dictionaries.popover.title.withFilters\r\n                : dictionaries.popover.title.noFilters}\r\n            </h4>\r\n            <p\r\n              id={descriptionId}\r\n              className={cn(\r\n                \"text-muted-foreground text-sm\",\r\n                filters.length > 0 && \"sr-only\"\r\n              )}\r\n            >\r\n              {filters.length > 0\r\n                ? dictionaries.popover.description.withFilters\r\n                : dictionaries.popover.description.noFilters}\r\n            </p>\r\n          </div>\r\n          {filters.length > 0 ? (\r\n            <SortableContent dir={dir} asChild>\r\n              <div\r\n                role=\"list\"\r\n                className=\"flex max-h-[300px] flex-col gap-2 overflow-y-auto p-1\"\r\n              >\r\n                {filters.length > 0 &&\r\n                  filters.map((filter, index) => (\r\n                    <DynamicFilterItem\r\n                      index={index}\r\n                      key={filter.filterId}\r\n                      filter={filter as FilterState}\r\n                      filterItemId={`${id}-filter-${filter.filterId}`}\r\n                    />\r\n                  ))}\r\n              </div>\r\n            </SortableContent>\r\n          ) : null}\r\n          <div className=\"flex w-full items-center gap-2\">\r\n            <Button size=\"sm\" className=\"rounded\" onClick={addFilter}>\r\n              {dictionaries.popover.buttonLabels.addFilter}\r\n            </Button>\r\n            {filters.length > 0 ? (\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"rounded\"\r\n                onClick={resetFilters}\r\n              >\r\n                {dictionaries.popover.buttonLabels.resetFilters}\r\n              </Button>\r\n            ) : null}\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </Sortable>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// جلب المكون بشكل ديناميكي لتسريع التحميل الأولي\r\nconst DynamicFilterItem = dynamic(async () => FilterMenuItem, {\r\n  ssr: false,\r\n  loading: () => <FilterMenuItemSkeleton />,\r\n})\r\n\r\n// المكون الرئيسي الذي يعرض كل الحقول (4 حقول) الخاصة بعنصر واحد من قائمة التصفية\r\nfunction FilterMenuItem({\r\n  index,\r\n  filter,\r\n  filterItemId,\r\n}: {\r\n  index: number\r\n  filterItemId: string\r\n  filter: FilterState\r\n}) {\r\n  const { items, dir, removeFilter } = useFilterState()\r\n\r\n  const item = items.find((item) => item.id === filter.id)\r\n  if (!item) return null\r\n\r\n  return (\r\n    <SortableItem dir={dir} value={filter.filterId} asChild>\r\n      <div\r\n        role=\"listitem\"\r\n        id={filterItemId}\r\n        tabIndex={-1}\r\n        className=\"flex items-center gap-2\"\r\n      >\r\n        <FilterMenuItemJoinSelector index={index} filterItemId={filterItemId} />\r\n        <FilterMenuItemSelector\r\n          filterItemId={filterItemId}\r\n          filter={filter}\r\n          item={item}\r\n        />\r\n        <FilterMenuItemOperatorSelector\r\n          filter={filter}\r\n          filterItemId={filterItemId}\r\n        />\r\n        <div className=\"min-w-36 flex-1\">\r\n          <FilterMenuItemValueInput\r\n            filterItemId={filterItemId}\r\n            filter={filter}\r\n            item={item}\r\n          />\r\n        </div>\r\n        <Button\r\n          aria-controls={filterItemId}\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"size-8 rounded\"\r\n          onClick={() => removeFilter(filter.filterId)}\r\n        >\r\n          <Trash2 />\r\n        </Button>\r\n        <SortableItemHandle asChild>\r\n          <Button variant=\"outline\" size=\"icon\" className=\"size-8 rounded\">\r\n            <GripVertical />\r\n          </Button>\r\n        </SortableItemHandle>\r\n      </div>\r\n    </SortableItem>\r\n  )\r\n}\r\n\r\n// هذا المكون يعرض بشكل مؤقت أثناء تحميل المكون الفعلي\r\nfunction FilterMenuItemSkeleton() {\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Skeleton className=\"h-8 min-w-[72px] rounded\" />\r\n      <Skeleton className=\"h-8 w-32 rounded\" />\r\n      <Skeleton className=\"h-8 w-32 rounded\" />\r\n      <Skeleton className=\"h-8 min-w-36 flex-1 rounded\" />\r\n      <Skeleton className=\"size-8 shrink-0 rounded\" />\r\n      <Skeleton className=\"size-8 shrink-0 rounded\" />\r\n    </div>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Join Selector\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل خيارات لتحديد طريقة الربط بين الفلترات AND / OR\r\nfunction FilterMenuItemJoinSelector({\r\n  index,\r\n  filterItemId,\r\n}: {\r\n  index: number\r\n  filterItemId: string\r\n}) {\r\n  const { joinOperator, dictionaries, dir, setJoinOperator } = useFilterState()\r\n\r\n  const dict = dictionaries.filterItem.JoinSelector\r\n  const joinOperatorListboxId = `${filterItemId}-join-operator-listbox`\r\n\r\n  return (\r\n    <div className=\"w-19 text-center\">\r\n      {index === 0 ? (\r\n        <span className=\"text-muted-foreground text-sm\">{dict.where}</span>\r\n      ) : index === 1 ? (\r\n        <Select\r\n          dir={dir}\r\n          value={joinOperator}\r\n          onValueChange={(value: FilterJoinState) => setJoinOperator(value)}\r\n        >\r\n          <SelectTrigger\r\n            aria-label=\"Select join operator\"\r\n            aria-controls={joinOperatorListboxId}\r\n            className=\"h-8 w-full rounded lowercase [&[data-size]]:h-8\"\r\n          >\r\n            <SelectValue placeholder={dict?.[joinOperator]} />\r\n          </SelectTrigger>\r\n          <SelectContent\r\n            id={joinOperatorListboxId}\r\n            position=\"popper\"\r\n            className=\"min-w-(--radix-select-trigger-width) lowercase\"\r\n          >\r\n            <SelectItem value={\"AND\"}>{dict.AND}</SelectItem>\r\n            <SelectItem value={\"OR\"}>{dict.OR}</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      ) : (\r\n        <span className=\"text-muted-foreground w-full text-sm lowercase\">\r\n          {dict?.[joinOperator]}\r\n        </span>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Selector\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل خيارات لتحديد العنصر الذي تريد تطبيق الفلتر عليه\r\nfunction FilterMenuItemSelector({\r\n  filterItemId,\r\n  filter,\r\n  item,\r\n}: {\r\n  filter: FilterState\r\n  filterItemId: string\r\n  item: FilterItem\r\n}) {\r\n  const { items, dictionaries, dir, updateFilter } = useFilterState()\r\n  const [open, setOpen] = React.useState(false)\r\n\r\n  const dict = dictionaries.filterItem.itemSelector\r\n  const fieldListboxId = `${filterItemId}-field-listbox`\r\n\r\n  const onItemSelect = React.useCallback(\r\n    (value: string, item: FilterItem) => {\r\n      updateFilter(filter.filterId, {\r\n        id: value,\r\n        variant: item.variant,\r\n        operator: getDefaultFilterOperator(item.variant),\r\n        value: \"\",\r\n      })\r\n\r\n      setOpen(false)\r\n    },\r\n    [filter.filterId, updateFilter]\r\n  )\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          role=\"combobox\"\r\n          aria-controls={fieldListboxId}\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          className=\"w-32 justify-between rounded font-normal\"\r\n        >\r\n          <span className=\"truncate\">{item.label}</span>\r\n          <ChevronsUpDownIcon className=\"opacity-50\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent\r\n        id={fieldListboxId}\r\n        align=\"start\"\r\n        className=\"w-40 origin-[var(--radix-popover-content-transform-origin)] p-0\"\r\n      >\r\n        <Command dir={dir}>\r\n          <CommandInput placeholder={dict.searchPlaceholder} />\r\n          <CommandList>\r\n            <CommandEmpty>{dict.noFieldsFound}</CommandEmpty>\r\n            <CommandGroup>\r\n              {items.map((item) => (\r\n                <CommandItem\r\n                  key={item.id}\r\n                  value={item.id}\r\n                  onSelect={(value) => onItemSelect(value, item)}\r\n                >\r\n                  <span className=\"truncate\">{item.label}</span>\r\n                  <CheckIcon\r\n                    className={cn(\r\n                      \"ms-auto\",\r\n                      item.id === filter.id ? \"opacity-100\" : \"opacity-0\"\r\n                    )}\r\n                  />\r\n                </CommandItem>\r\n              ))}\r\n            </CommandGroup>\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Operator Selector\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل خيارات لتحديد نوع شرط الفلتر\r\nfunction FilterMenuItemOperatorSelector({\r\n  filter,\r\n  filterItemId,\r\n}: {\r\n  filter: FilterState\r\n  filterItemId: string\r\n}) {\r\n  const { dir, dictionaries, updateFilter } = useFilterState()\r\n\r\n  const dict = dictionaries.filterItem.operatorSelector\r\n  const operatorListboxId = `${filterItemId}-operator-listbox`\r\n\r\n  const filterOperators = React.useMemo(\r\n    () => getFilterOperators(filter.variant, dict.operators),\r\n    [dict.operators, filter.variant]\r\n  )\r\n\r\n  // وجود عدة شروط في هذه الدالة هو لضمان\r\n  // تغيير نوع الفلتر من رقم إلى نطاق رقم أو تاريخ إلى نطاق تاريخ\r\n  // عندما يتم تحديد شرط \"بين\" لأنها غير مدرجة ضمن خيارات انواع الفلتر\r\n  const onOperatorChange = React.useCallback(\r\n    (operator: Operator) => {\r\n      if (filter.variant === \"number\" || filter.variant === \"numberRange\") {\r\n        if (operator === \"isBetween\") {\r\n          updateFilter(filter.filterId, {\r\n            operator: \"isBetween\",\r\n            variant: \"numberRange\",\r\n            value: [\"\", \"\"],\r\n          })\r\n        } else {\r\n          updateFilter(filter.filterId, {\r\n            operator: operator,\r\n            variant: \"number\",\r\n            value: Array.isArray(filter.value) ? filter.value[0] : filter.value,\r\n          })\r\n        }\r\n        return\r\n      }\r\n\r\n      if (filter.variant === \"date\" || filter.variant === \"dateRange\") {\r\n        if (operator === \"isBetween\") {\r\n          updateFilter(filter.filterId, {\r\n            operator: \"isBetween\",\r\n            variant: \"dateRange\",\r\n            value: [\"\", \"\"],\r\n          })\r\n        } else {\r\n          updateFilter(filter.filterId, {\r\n            operator: operator,\r\n            variant: \"date\",\r\n            value: Array.isArray(filter.value) ? filter.value[0] : filter.value,\r\n          })\r\n        }\r\n        return\r\n      }\r\n\r\n      if (operator === \"isEmpty\" || operator === \"isNotEmpty\") {\r\n        updateFilter(filter.filterId, {\r\n          operator: operator,\r\n          value: operator === \"isEmpty\" ? \"empty\" : \"notEmpty\",\r\n        })\r\n        return\r\n      }\r\n\r\n      updateFilter(filter.filterId, {\r\n        operator: operator,\r\n      })\r\n    },\r\n    [filter.filterId, filter.value, filter.variant, updateFilter]\r\n  )\r\n\r\n  return (\r\n    <Select dir={dir} value={filter.operator} onValueChange={onOperatorChange}>\r\n      <SelectTrigger\r\n        aria-controls={operatorListboxId}\r\n        className=\"h-8 w-32 rounded lowercase [&[data-size]]:h-8\"\r\n      >\r\n        <div className=\"truncate\">\r\n          <SelectValue placeholder={filter.operator} />\r\n        </div>\r\n      </SelectTrigger>\r\n      <SelectContent\r\n        id={operatorListboxId}\r\n        className=\"origin-[var(--radix-select-content-transform-origin)]\"\r\n      >\r\n        {filterOperators.map((operator) => (\r\n          <SelectItem\r\n            key={operator.value}\r\n            value={operator.value}\r\n            className=\"lowercase\"\r\n          >\r\n            {operator.label}\r\n          </SelectItem>\r\n        ))}\r\n      </SelectContent>\r\n    </Select>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يحدد الحقل المناسب التي يجب أن يعرضه بناءً على نوع او وشرط الفلتر\r\nfunction FilterMenuItemValueInput({\r\n  filter,\r\n  filterItemId,\r\n  item,\r\n}: {\r\n  filter: FilterState\r\n  item: FilterItem\r\n  filterItemId: string\r\n}) {\r\n  const inputId = `${filterItemId}-input`\r\n\r\n  if (filter.operator === \"isEmpty\" || filter.operator === \"isNotEmpty\") {\r\n    return (\r\n      <FilterMenuItemValueInputEmptyOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n\r\n  if (filter.variant === \"text\" && item.variant === \"text\") {\r\n    return (\r\n      <FilterMenuItemValueInputTextOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n\r\n  if (filter.variant === \"number\" && item.variant === \"number\") {\r\n    return (\r\n      <FilterMenuItemValueInputNumberOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n\r\n  if (filter.variant === \"numberRange\" && item.variant === \"number\") {\r\n    return (\r\n      <FilterMenuItemValueInputNumberRangeOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n\r\n  if (\r\n    (filter.variant === \"select\" && item.variant === \"select\") ||\r\n    (filter.variant === \"multiSelect\" && item.variant === \"multiSelect\")\r\n  ) {\r\n    return (\r\n      <FilterMenuItemValueInputSelectOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n\r\n  if (filter.variant === \"boolean\" && item.variant === \"boolean\") {\r\n    return (\r\n      <FilterMenuItemValueInputBooleanOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n\r\n  if (filter.variant === \"date\" && item.variant === \"date\") {\r\n    return (\r\n      <FilterMenuItemValueInputDateOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n\r\n  if (filter.variant === \"dateRange\" && item.variant === \"date\") {\r\n    return (\r\n      <FilterMenuItemValueInputDateRangeOperator\r\n        inputId={inputId}\r\n        filter={filter}\r\n        item={item}\r\n      />\r\n    )\r\n  }\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Empty Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل وهمي عندما يكون نوع الـ operator isEmpty أو isNotEmpty\r\nfunction FilterMenuItemValueInputEmptyOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem\r\n  filter: FilterState\r\n}) {\r\n  return (\r\n    <div\r\n      id={inputId}\r\n      role=\"status\"\r\n      aria-label={`${item?.label} filter is ${\r\n        filter.operator === \"isEmpty\" ? \"empty\" : \"not empty\"\r\n      }`}\r\n      aria-live=\"polite\"\r\n      className=\"dark:bg-input/30 h-8 w-full rounded border bg-transparent\"\r\n    />\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Text Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل ادخال نص عندما يكون نوع الـ operator text\r\nfunction FilterMenuItemValueInputTextOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem & { variant: \"text\" }\r\n  filter: FilterState & { variant: \"text\" }\r\n}) {\r\n  const { updateFilter } = useFilterState()\r\n\r\n  return (\r\n    <div className={cn(\"relative w-full\")}>\r\n      <Input\r\n        id={inputId}\r\n        type={\"search\"}\r\n        aria-label={`${item?.label} filter value`}\r\n        aria-describedby={`${inputId}-description`}\r\n        inputMode={\"search\"}\r\n        placeholder={item?.placeholder}\r\n        className={cn(\r\n          // base\r\n          \"h-8 max-h-full w-full rounded-sm ps-8\",\r\n          // hide cancel button and decoration\r\n          \"[&::-webkit-search-cancel-button]:hidden [&::-webkit-search-decoration]:hidden\"\r\n        )}\r\n        defaultValue={\r\n          typeof filter.value === \"string\" ? filter.value : undefined\r\n        }\r\n        onChange={(event) =>\r\n          updateFilter(filter.filterId, {\r\n            value: event.target.value,\r\n          })\r\n        }\r\n      />\r\n      <div\r\n        className={cn(\r\n          // base\r\n          \"pointer-events-none absolute start-2 bottom-0 flex h-full items-center justify-center\",\r\n          // text color\r\n          \"text-muted-foreground/60\"\r\n        )}\r\n      >\r\n        <SearchIcon className=\"size-5 shrink-0\" aria-hidden=\"true\" />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Number Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل ادخال رقم عندما يكون نوع الـ operator number\r\nfunction FilterMenuItemValueInputNumberOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem & { variant: \"number\" }\r\n  filter: FilterState & { variant: \"number\" }\r\n}) {\r\n  const { updateFilter } = useFilterState()\r\n\r\n  const rangeMin = item.range?.min\r\n  const rangeMax = item.range?.max\r\n\r\n  const enableChange = React.useCallback(\r\n    (value: string) => {\r\n      const val = Number(value)\r\n\r\n      if (rangeMin !== undefined && val < rangeMin) {\r\n        toast.error(`The minimum value must be greater than ${rangeMin}`)\r\n        return false\r\n      }\r\n      if (rangeMax !== undefined && val > rangeMax) {\r\n        toast.error(`The maximum value must be less than ${rangeMax}`)\r\n        return false\r\n      }\r\n\r\n      return true\r\n    },\r\n    [rangeMax, rangeMin]\r\n  )\r\n\r\n  const onValueChange = React.useCallback(\r\n    (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      const value = event.target.value\r\n\r\n      if (!enableChange(value)) return\r\n\r\n      updateFilter(filter.filterId, {\r\n        value: value,\r\n      })\r\n    },\r\n    [enableChange, filter.filterId, updateFilter]\r\n  )\r\n\r\n  switch (filter.operator) {\r\n    default:\r\n      return (\r\n        <div className=\"relative w-full\">\r\n          <Input\r\n            id={inputId}\r\n            type={\"number\"}\r\n            aria-label={`${item?.label} filter value`}\r\n            aria-describedby={`${inputId}-description`}\r\n            aria-valuemin={rangeMin}\r\n            aria-valuemax={rangeMax}\r\n            min={rangeMin}\r\n            max={rangeMax}\r\n            step={item.step ?? 1}\r\n            inputMode={\"numeric\"}\r\n            placeholder={item.placeholder}\r\n            className={cn(\r\n              \"h-8 [&>input]:w-full [&>input]:rounded-sm\",\r\n              item.unit && \"[&>input]:pr-8\"\r\n            )}\r\n            defaultValue={filter.value ?? \"\"}\r\n            onChange={onValueChange}\r\n          />\r\n          {item?.unit && (\r\n            <span className=\"bg-accent text-muted-foreground absolute top-px right-px bottom-px flex items-center rounded-r-sm px-2 text-sm\">\r\n              {item.unit}\r\n            </span>\r\n          )}\r\n        </div>\r\n      )\r\n  }\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Number Range Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقلين ادخال رقمي الأول للقيمة الصغر والثاني للقيمة الأكبر عندما يكون نوع الـ operator numberRange\r\nfunction FilterMenuItemValueInputNumberRangeOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem & { variant: \"number\" }\r\n  filter: FilterState & { variant: \"numberRange\" }\r\n}) {\r\n  const { updateFilter } = useFilterState()\r\n  const range = item?.range\r\n\r\n  const rangeMin = range?.min\r\n  const rangeMax = range?.max\r\n\r\n  const enableChange = React.useCallback(\r\n    (value: string) => {\r\n      const val = Number(value)\r\n\r\n      if (rangeMin !== undefined && val < rangeMin) {\r\n        toast.error(`The minimum value must be greater than ${rangeMin}`)\r\n        return false\r\n      }\r\n      if (rangeMax !== undefined && val > rangeMax) {\r\n        toast.error(`The maximum value must be less than ${rangeMax}`)\r\n        return false\r\n      }\r\n\r\n      return true\r\n    },\r\n    [rangeMax, rangeMin]\r\n  )\r\n\r\n  const onValueChange = React.useCallback(\r\n    (event: React.ChangeEvent<HTMLInputElement>, from: \"min\" | \"max\") => {\r\n      const value = event.target.value\r\n\r\n      if (!enableChange(value)) return\r\n\r\n      if (from === \"min\") {\r\n        updateFilter(filter.filterId, {\r\n          value: [value, filter.value[0]],\r\n        })\r\n        return\r\n      }\r\n\r\n      if (from === \"max\") {\r\n        updateFilter(filter.filterId, {\r\n          value: [filter.value[0] ?? \"\", value],\r\n        })\r\n        return\r\n      }\r\n    },\r\n    [enableChange, filter.filterId, filter.value, updateFilter]\r\n  )\r\n\r\n  return (\r\n    <div data-slot=\"range\" className={cn(\"flex w-full items-center gap-2\")}>\r\n      <Input\r\n        id={`${inputId}-min`}\r\n        type=\"number\"\r\n        aria-label={`${item?.label} minimum value`}\r\n        aria-valuemin={rangeMin}\r\n        aria-valuemax={rangeMax}\r\n        data-slot=\"range-min\"\r\n        inputMode=\"numeric\"\r\n        placeholder={rangeMin?.toString()}\r\n        min={rangeMin}\r\n        max={rangeMax}\r\n        step={item.step ?? 1}\r\n        className=\"h-8 w-full rounded\"\r\n        defaultValue={filter.value[0] ?? \"\"}\r\n        onChange={(event) => onValueChange(event, \"min\")}\r\n      />\r\n      <span className=\"text-muted-foreground sr-only shrink-0\">to</span>\r\n      <Input\r\n        id={`${inputId}-max`}\r\n        type=\"number\"\r\n        aria-label={`${item?.label} maximum value`}\r\n        aria-valuemin={rangeMin}\r\n        aria-valuemax={rangeMax}\r\n        data-slot=\"range-max\"\r\n        inputMode=\"numeric\"\r\n        placeholder={rangeMax?.toString()}\r\n        min={rangeMin}\r\n        max={rangeMax}\r\n        step={item.step ?? 1}\r\n        className=\"h-8 w-full rounded\"\r\n        defaultValue={filter.value[1] ?? \"\"}\r\n        onChange={(event) => onValueChange(event, \"max\")}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Select Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل اختيار قيمة واحدة او اكثر من بين قائمة محددة مسبقًا عندما يكون نوع الـ operator select أو multiSelect\r\nfunction FilterMenuItemValueInputSelectOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem & { variant: \"select\" | \"multiSelect\" }\r\n  filter: FilterState & { variant: \"select\" | \"multiSelect\" }\r\n}) {\r\n  const { updateFilter } = useFilterState()\r\n  const [open, setIsOpen] = React.useState(false)\r\n\r\n  const inputListboxId = `${inputId}-listbox`\r\n  const multiple = filter.variant === \"multiSelect\"\r\n\r\n  const selectedValues = multiple\r\n    ? Array.isArray(filter.value)\r\n      ? filter.value\r\n      : []\r\n    : typeof filter.value === \"string\"\r\n      ? filter.value\r\n      : undefined\r\n\r\n  return (\r\n    <Faceted\r\n      open={open}\r\n      onOpenChange={setIsOpen}\r\n      value={selectedValues}\r\n      onValueChange={(value) => {\r\n        updateFilter(filter.filterId, {\r\n          value,\r\n        })\r\n      }}\r\n      multiple={multiple}\r\n    >\r\n      <FacetedTrigger asChild>\r\n        <Button\r\n          id={inputId}\r\n          aria-controls={inputListboxId}\r\n          aria-label={`${item?.label} filter value${multiple ? \"s\" : \"\"}`}\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          className=\"w-full rounded font-normal\"\r\n        >\r\n          <FacetedBadgeList\r\n            options={item?.options}\r\n            placeholder={\r\n              item?.placeholder ?? `Select option${multiple ? \"s\" : \"\"}...`\r\n            }\r\n          />\r\n        </Button>\r\n      </FacetedTrigger>\r\n      <FacetedContent\r\n        id={inputListboxId}\r\n        className=\"w-[200px] origin-[var(--radix-popover-content-transform-origin)]\"\r\n      >\r\n        <FacetedInput\r\n          aria-label={`Search ${item?.label} options`}\r\n          placeholder={item?.placeholder ?? \"Search options...\"}\r\n        />\r\n        <FacetedList>\r\n          <FacetedEmpty>No options found.</FacetedEmpty>\r\n          <FacetedGroup>\r\n            {item?.options?.map((option) => (\r\n              <FacetedItem\r\n                className=\"gap-3\"\r\n                key={option.value}\r\n                value={option.value}\r\n              >\r\n                {option.icon && (\r\n                  <option.icon className=\"text-muted-foreground size-4\" />\r\n                )}\r\n                <span>{option.label}</span>\r\n                {option.count && (\r\n                  <Badge\r\n                    variant=\"outline\"\r\n                    className=\"ms-auto flex h-full items-center justify-center rounded-sm px-1 py-px font-mono text-xs\"\r\n                  >\r\n                    {option.count}\r\n                  </Badge>\r\n                )}\r\n              </FacetedItem>\r\n            ))}\r\n          </FacetedGroup>\r\n        </FacetedList>\r\n      </FacetedContent>\r\n    </Faceted>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Boolean Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض حقل خيارات الخيار الأول (صحيح) والخيار الثاني (خطأ) عندما يكون نوع الـ operator boolean\r\nfunction FilterMenuItemValueInputBooleanOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem & { variant: \"boolean\" }\r\n  filter: FilterState & { variant: \"boolean\" }\r\n}) {\r\n  const { updateFilter } = useFilterState()\r\n\r\n  return (\r\n    <Select\r\n      value={String(filter.value)}\r\n      onValueChange={(value) =>\r\n        updateFilter(filter.filterId, {\r\n          value,\r\n        })\r\n      }\r\n    >\r\n      <SelectTrigger\r\n        id={inputId}\r\n        aria-controls={`${inputId}-listbox`}\r\n        aria-label={`${item?.label} boolean filter`}\r\n        className=\"h-8 w-full rounded [&[data-size]]:h-8\"\r\n      >\r\n        <SelectValue placeholder={\"Select option...\"} />\r\n      </SelectTrigger>\r\n      <SelectContent id={`${inputId}-listbox`}>\r\n        <SelectItem value=\"true\">True</SelectItem>\r\n        <SelectItem value=\"false\">False</SelectItem>\r\n      </SelectContent>\r\n    </Select>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Date Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض زر التاريخ عند النقر على الزر يتم عرض تقويم لإختيار التاريخ عندما يكون نوع الـ operator date\r\nfunction FilterMenuItemValueInputDateOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem & { variant: \"date\" }\r\n  filter: FilterState & { variant: \"date\" }\r\n}) {\r\n  const { updateFilter } = useFilterState()\r\n  const range = item?.range\r\n\r\n  return (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          aria-label={`${item?.label} date filter`}\r\n          aria-controls={`${inputId}-listbox`}\r\n          className=\"w-full justify-between rounded font-normal\"\r\n        >\r\n          {filter.value\r\n            ? formatDate(new Date(Number(filter.value)), \"yyyy-MM-dd\")\r\n            : \"Select date\"}\r\n          <ChevronDown />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent\r\n        id={`${inputId}-listbox`}\r\n        className=\"w-auto overflow-hidden p-0\"\r\n        align=\"start\"\r\n      >\r\n        <Calendar\r\n          dir=\"rtl\"\r\n          locale={ar}\r\n          mode=\"single\"\r\n          disabled={[\r\n            ...(range?.min ? [{ before: new Date(range.min) }] : []),\r\n            ...(range?.max ? [{ after: new Date(range.max) }] : []),\r\n          ]}\r\n          selected={new Date(Number(filter.value))}\r\n          captionLayout=\"dropdown\"\r\n          onSelect={(date) => {\r\n            updateFilter(filter.filterId, {\r\n              value: date?.getTime().toString() ?? \"\",\r\n            })\r\n          }}\r\n        />\r\n      </PopoverContent>\r\n    </Popover>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Item Value Input Date Range Operator\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\n// مكون يعرض زر التاريخ عند النقر على الزر يتم عرض تقويم لإختيار تاريخين الأول للقيمة الصغر والثاني للقيمة الأكبر عندما يكون نوع الـ operator dateRange\r\nfunction FilterMenuItemValueInputDateRangeOperator({\r\n  inputId,\r\n  filter,\r\n  item,\r\n}: {\r\n  inputId: string\r\n  item: FilterItem & { variant: \"date\" }\r\n  filter: FilterState & { variant: \"dateRange\"; operator: \"isBetween\" }\r\n}) {\r\n  const { updateFilter } = useFilterState()\r\n\r\n  const from = filter.value[0] ? new Date(Number(filter.value[0])) : undefined\r\n  const to = filter.value[1] ? new Date(Number(filter.value[1])) : undefined\r\n\r\n  const range = item?.range\r\n\r\n  return (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          aria-label={`${item?.label} date range filter`}\r\n          aria-controls={`${inputId}-listbox`}\r\n          className=\"w-full justify-between rounded font-normal\"\r\n        >\r\n          {\r\n            <span className=\"truncate\">\r\n              {from && to\r\n                ? `From -> ${formatDate(from, \"yyyy/MM/dd\")} - To -> ${formatDate(to, \"yyyy/MM/dd\")}`\r\n                : \"Pick a date\"}\r\n            </span>\r\n          }\r\n          <ChevronDown />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent\r\n        id={`${inputId}-listbox`}\r\n        className=\"w-auto overflow-hidden p-0\"\r\n        align=\"start\"\r\n      >\r\n        <Calendar\r\n          disabled={[\r\n            ...(range?.min ? [{ before: new Date(range.min) }] : []),\r\n            ...(range?.max ? [{ after: new Date(range.max) }] : []),\r\n          ]}\r\n          mode=\"range\"\r\n          selected={{\r\n            from: from ?? new Date(),\r\n            to: to ?? new Date(),\r\n          }}\r\n          captionLayout=\"dropdown\"\r\n          onSelect={(date) => {\r\n            updateFilter(filter.filterId, {\r\n              value: [\r\n                (date?.from?.getTime() ?? \"\").toString(),\r\n                (date?.to?.getTime() ?? \"\").toString(),\r\n              ],\r\n            })\r\n          }}\r\n        />\r\n      </PopoverContent>\r\n    </Popover>\r\n  )\r\n}\r\n", "type": "registry:component", "target": "src/components/toolbar/toolbar-filter.tsx"}, {"path": "registry/new-york/blocks/toolbar/toolbar-search.tsx", "content": "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { SearchIcon } from \"lucide-react\"\r\nimport { useQueryState } from \"nuqs\"\r\n\r\nimport { DynamicObject } from \"@/types/globals\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { useDebouncedCallback } from \"@/hooks/use-debounced-callback\"\r\nimport { Input } from \"@/components/ui/input\"\r\n\r\nimport { useToolbar } from \"./hooks\"\r\nimport { getSearchStateParser } from \"./lib\"\r\n\r\ntype SearchItem<T extends DynamicObject = DynamicObject> = {\r\n  id: Extract<keyof T, string>\r\n  placeholder?: string\r\n}\r\n\r\ntype SearchProps<T extends DynamicObject = DynamicObject> = {\r\n  item: SearchItem<T>\r\n  shallow?: boolean\r\n  debounceMs?: number\r\n  className?: string\r\n}\r\n\r\nexport function ToolbarSearch<T extends DynamicObject = DynamicObject>({\r\n  item,\r\n  shallow = false,\r\n  debounceMs = 300,\r\n  className,\r\n}: SearchProps<T>) {\r\n  const { startTransition } = useToolbar()\r\n  const [value, setValue] = useQueryState(\r\n    \"search\",\r\n    getSearchStateParser(item.id)\r\n      .withDefault({ id: item.id, value: \"\" })\r\n      .withOptions({\r\n        startTransition,\r\n        clearOnDefault: true,\r\n        shallow,\r\n        throttleMs: debounceMs,\r\n      })\r\n  )\r\n\r\n  const debouncedSetValue = useDebouncedCallback(setValue, debounceMs)\r\n\r\n  const onSearchChange = React.useCallback(\r\n    (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      debouncedSetValue({ id: item.id, value: event.target.value })\r\n    },\r\n    [debouncedSetValue, item.id]\r\n  )\r\n\r\n  return (\r\n    <div className={cn(\"relative w-full\", className)}>\r\n      <Input\r\n        aria-label=\"Search\"\r\n        type=\"search\"\r\n        placeholder={item.placeholder}\r\n        defaultValue={value?.value}\r\n        onChange={onSearchChange}\r\n        className={cn(\r\n          // base\r\n          \"h-8 max-h-full w-full ps-8\",\r\n          // hide cancel button and decoration\r\n          \"[&::-webkit-search-cancel-button]:hidden [&::-webkit-search-decoration]:hidden\"\r\n        )}\r\n      />\r\n      <div\r\n        className={cn(\r\n          // base\r\n          \"pointer-events-none absolute start-2 bottom-0 flex h-full items-center justify-center\",\r\n          // text color\r\n          \"text-muted-foreground/60\"\r\n        )}\r\n      >\r\n        <SearchIcon className=\"size-5 shrink-0\" aria-hidden=\"true\" />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n", "type": "registry:component", "target": "src/components/toolbar/toolbar-search.tsx"}, {"path": "registry/new-york/blocks/toolbar/toolbar-sorting.tsx", "content": "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ArrowDownUp, ChevronsUpDown, GripVertical, Trash2 } from \"lucide-react\"\r\nimport { useQueryState } from \"nuqs\"\r\n\r\nimport { DynamicObject } from \"@/types/globals\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\"\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\"\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\"\r\nimport {\r\n  Sortable,\r\n  SortableContent,\r\n  SortableItem,\r\n  SortableItemHandle,\r\n} from \"@/components/ui/sortable\"\r\n\r\nimport { useToolbar } from \"./hooks\"\r\nimport { getSortingStateParser, sortingOrders } from \"./lib\"\r\nimport {\r\n  OnSortRemove,\r\n  OnSortUpdate,\r\n  SortDirection,\r\n  SortingItemProps,\r\n  ToolbarSortingProps,\r\n} from \"./types\"\r\n\r\nexport function ToolbarSorting<T extends DynamicObject = DynamicObject>({\r\n  items,\r\n  className,\r\n}: ToolbarSortingProps<T>) {\r\n  const id = React.useId()\r\n  const labelId = React.useId()\r\n  const descriptionId = React.useId()\r\n  const addButtonRef = React.useRef<HTMLButtonElement>(null)\r\n  const { startTransition, dictionaries, dir } = useToolbar()\r\n  const dict = dictionaries.sorting\r\n\r\n  const [sorting, setSorting] = useQueryState(\r\n    \"sort\",\r\n    getSortingStateParser(items.map((item) => item.id))\r\n      .withDefault([])\r\n      .withOptions({ clearOnDefault: true, shallow: false, startTransition })\r\n  )\r\n\r\n  const addSort = React.useCallback(() => {\r\n    const firstItem = items[0]\r\n    if (!firstItem) return\r\n\r\n    setSorting((prevSorting) => [\r\n      ...prevSorting,\r\n      { id: firstItem.id, value: \"asc\" },\r\n    ])\r\n  }, [setSorting, items])\r\n\r\n  const updateSort: OnSortUpdate = React.useCallback(\r\n    (sortId, updates) => {\r\n      setSorting((prevSorting) => {\r\n        if (!prevSorting) return prevSorting\r\n        return prevSorting.map((sort) =>\r\n          sort.id === sortId ? { ...sort, ...updates } : sort\r\n        )\r\n      })\r\n    },\r\n    [setSorting]\r\n  )\r\n\r\n  const removeSort: OnSortRemove = React.useCallback(\r\n    (sortId) => {\r\n      setSorting((prevSorting) =>\r\n        prevSorting.filter((item) => item.id !== sortId)\r\n      )\r\n    },\r\n    [setSorting]\r\n  )\r\n\r\n  const resetSorting = React.useCallback(() => setSorting([]), [setSorting])\r\n\r\n  return (\r\n    <Sortable\r\n      value={sorting}\r\n      onValueChange={setSorting}\r\n      getItemValue={(item) => item.id}\r\n    >\r\n      <Popover>\r\n        <PopoverTrigger asChild>\r\n          <Button variant=\"outline\" size=\"sm\" className={className}>\r\n            <ArrowDownUp />\r\n            {dict.triggerButtonLabel}\r\n            {sorting.length > 0 && (\r\n              <Badge\r\n                variant=\"secondary\"\r\n                className=\"h-[18.24px] rounded-[3.2px] px-[5.12px] font-mono text-[10.4px] font-normal\"\r\n              >\r\n                {sorting.length}\r\n              </Badge>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent\r\n          dir={dir}\r\n          aria-labelledby={labelId}\r\n          aria-describedby={descriptionId}\r\n          className=\"flex w-full max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-3.5 p-4 sm:min-w-[380px]\"\r\n        >\r\n          <div className=\"flex flex-col gap-1\">\r\n            <h4 id={labelId} className=\"leading-none font-medium\">\r\n              {sorting.length > 0\r\n                ? dict.popover.title.withSorting\r\n                : dict.popover.title.noSorting}\r\n            </h4>\r\n            <p\r\n              id={descriptionId}\r\n              className={cn(\r\n                \"text-muted-foreground text-sm\",\r\n                sorting.length > 0 && \"sr-only\"\r\n              )}\r\n            >\r\n              {dict.popover.description}\r\n            </p>\r\n          </div>\r\n          {sorting.length > 0 && (\r\n            <SortableContent asChild>\r\n              <div\r\n                role=\"list\"\r\n                className=\"flex max-h-[300px] flex-col gap-2 overflow-y-auto p-1\"\r\n              >\r\n                {sorting.map((sort) => (\r\n                  <SortingItem\r\n                    key={sort.id}\r\n                    sort={sort}\r\n                    sortItemId={`${id}-sort-${sort.id}`}\r\n                    items={items}\r\n                    updateSort={updateSort}\r\n                    removeSort={removeSort}\r\n                  />\r\n                ))}\r\n              </div>\r\n            </SortableContent>\r\n          )}\r\n          <div className=\"flex w-full items-center gap-2\">\r\n            <Button\r\n              size=\"sm\"\r\n              className=\"rounded\"\r\n              ref={addButtonRef}\r\n              onClick={addSort}\r\n              disabled={items.length === 0}\r\n            >\r\n              {dict.popover.buttonLabels.addSorting}\r\n            </Button>\r\n            {sorting.length > 0 && (\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"rounded\"\r\n                onClick={resetSorting}\r\n              >\r\n                {dict.popover.buttonLabels.resetSorting}\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </Sortable>\r\n  )\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Sorting Item\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\nfunction SortingItem({\r\n  sort,\r\n  items,\r\n  sortItemId,\r\n  updateSort,\r\n  removeSort,\r\n}: SortingItemProps) {\r\n  const [open, setOpen] = React.useState(false)\r\n  const { dictionaries, dir } = useToolbar()\r\n\r\n  const fieldListboxId = `${sortItemId}-field-listbox`\r\n  const fieldTriggerId = `${sortItemId}-field-trigger`\r\n  const directionListboxId = `${sortItemId}-direction-listbox`\r\n\r\n  const dict = dictionaries.sorting.sortingItem\r\n\r\n  return (\r\n    <SortableItem value={sort.id} asChild>\r\n      <div\r\n        role=\"listitem\"\r\n        id={sortItemId}\r\n        tabIndex={-1}\r\n        className=\"flex items-center gap-2\"\r\n      >\r\n        <Popover open={open} onOpenChange={setOpen}>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              id={fieldTriggerId}\r\n              role=\"combobox\"\r\n              aria-controls={fieldListboxId}\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"w-44 justify-between rounded font-normal\"\r\n            >\r\n              <span className=\"truncate\">\r\n                {items.find((item) => item.id === sort.id)?.label}\r\n              </span>\r\n              <ChevronsUpDown className=\"opacity-50\" />\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent\r\n            dir={dir}\r\n            id={fieldListboxId}\r\n            className=\"w-[var(--radix-popover-trigger-width)] origin-[var(--radix-popover-content-transform-origin)] p-0\"\r\n          >\r\n            <Command dir={dir}>\r\n              <CommandInput\r\n                placeholder={dict.fieldSelector.searchPlaceholder}\r\n              />\r\n              <CommandList>\r\n                <CommandEmpty>{dict.fieldSelector.noFieldsFound}</CommandEmpty>\r\n                <CommandGroup>\r\n                  {items.map((column) => (\r\n                    <CommandItem\r\n                      key={column.id}\r\n                      value={column.id}\r\n                      onSelect={(value) =>\r\n                        updateSort(sort.id, {\r\n                          id: value,\r\n                        })\r\n                      }\r\n                    >\r\n                      <span className=\"truncate\">{column.label}</span>\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              </CommandList>\r\n            </Command>\r\n          </PopoverContent>\r\n        </Popover>\r\n        <Select\r\n          dir={dir}\r\n          value={sort.value}\r\n          onValueChange={(value: SortDirection) =>\r\n            updateSort(sort.id, { value: value })\r\n          }\r\n        >\r\n          <SelectTrigger\r\n            aria-controls={directionListboxId}\r\n            className=\"h-8 w-24 rounded [&[data-size]]:h-8\"\r\n          >\r\n            <SelectValue />\r\n          </SelectTrigger>\r\n          <SelectContent\r\n            id={directionListboxId}\r\n            className=\"min-w-[var(--radix-select-trigger-width)] origin-[var(--radix-select-content-transform-origin)]\"\r\n          >\r\n            {sortingOrders.map((order: SortDirection) => (\r\n              <SelectItem key={order} value={order}>\r\n                {dict.directionSelector[order]}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectContent>\r\n        </Select>\r\n        <Button\r\n          aria-controls={sortItemId}\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"size-8 shrink-0 rounded\"\r\n          onClick={() => removeSort(sort.id)}\r\n        >\r\n          <Trash2 />\r\n        </Button>\r\n        <SortableItemHandle asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"size-8 shrink-0 rounded\"\r\n          >\r\n            <GripVertical />\r\n          </Button>\r\n        </SortableItemHandle>\r\n      </div>\r\n    </SortableItem>\r\n  )\r\n}\r\n", "type": "registry:component", "target": "src/components/toolbar/toolbar-sorting.tsx"}, {"path": "registry/new-york/blocks/toolbar/index.ts", "content": "export * from \"./toolbar\"\r\nexport * from \"./toolbar-filter\"\r\nexport * from \"./toolbar-search\"\r\nexport * from \"./toolbar-sorting\"\r\n", "type": "registry:file", "target": "src/components/toolbar/index.ts"}, {"path": "registry/new-york/blocks/toolbar/lib/toolbar-lib.ts", "content": "import React from \"react\"\r\nimport {\r\n  createSearchParamsCache,\r\n  parseAsStringEnum,\r\n  SearchParams,\r\n} from \"nuqs/server\"\r\n\r\nimport { Locale } from \"@/types/globals\"\r\n\r\nimport toolbarDictionariesAr from \"../locales/ar\"\r\nimport toolbarDictionariesEn from \"../locales/en\"\r\nimport {\r\n  getFilterQueryParser,\r\n  getFiltersStateParser,\r\n} from \"./toolbar-filter-lib\"\r\nimport {\r\n  getSearchQueryParser,\r\n  getSearchStateParser,\r\n} from \"./toolbar-search-lib\"\r\nimport {\r\n  getSortingQueryParser,\r\n  getSortingStateParser,\r\n} from \"./toolbar-sorting-lib\"\r\n\r\nexport type GetToolbarDictionaryResult = ReturnType<typeof getToolbarDictionary>\r\n\r\nconst toolbarDictionaries = {\r\n  en: toolbarDictionariesEn,\r\n  ar: toolbarDictionariesAr,\r\n}\r\n\r\nexport const getToolbarDictionary = (locale: Locale) => {\r\n  return toolbarDictionaries[locale]\r\n}\r\n\r\nexport const getToolbarQueryParser = React.cache(\r\n  async (searchParams: Promise<SearchParams> | SearchParams) => {\r\n    const searchParamsCache = createSearchParamsCache({\r\n      filters: getFiltersStateParser().withDefault([]),\r\n      join: parseAsStringEnum([\"AND\", \"OR\"]).withDefault(\"AND\"),\r\n      search: getSearchStateParser().withDefault({}),\r\n      sort: getSortingStateParser().withDefault([]),\r\n    })\r\n\r\n    const searchParamsCacheParsed = searchParamsCache.parse(await searchParams)\r\n\r\n    const filters = getFilterQueryParser({\r\n      filters: searchParamsCacheParsed.filters,\r\n      join: searchParamsCacheParsed.join,\r\n    })\r\n\r\n    const search = getSearchQueryParser(searchParamsCacheParsed.search)\r\n    const sorting = getSortingQueryParser(searchParamsCacheParsed.sort)\r\n\r\n    const where = { ...filters, ...search }\r\n    const orderBy = sorting\r\n\r\n    return { where, orderBy }\r\n  }\r\n)\r\n", "type": "registry:lib", "target": "src/components/toolbar/lib/toolbar-lib.ts"}, {"path": "registry/new-york/blocks/toolbar/lib/toolbar-filter-lib.ts", "content": "import * as React from \"react\"\r\nimport { endOfDay, startOfDay } from \"date-fns\"\r\nimport { createParser } from \"nuqs/server\"\r\nimport { z } from \"zod/v4\"\r\n\r\nimport {\r\n  FilterJoinState,\r\n  FilterState,\r\n  FilterVariant,\r\n  ToolbarDictionaries,\r\n} from \"../types\"\r\n\r\nconst textOperators = [\r\n  \"iLike\",\r\n  \"notILike\",\r\n  \"eq\",\r\n  \"ne\",\r\n  \"isEmpty\",\r\n  \"isNotEmpty\",\r\n] as const\r\nconst numericOperators = [\r\n  \"eq\",\r\n  \"ne\",\r\n  \"lt\",\r\n  \"lte\",\r\n  \"gt\",\r\n  \"gte\",\r\n  \"isBetween\",\r\n  \"isEmpty\",\r\n  \"isNotEmpty\",\r\n] as const\r\nconst dateOperators = [\r\n  \"eq\",\r\n  \"ne\",\r\n  \"lt\",\r\n  \"lte\",\r\n  \"gt\",\r\n  \"gte\",\r\n  \"isBetween\",\r\n  \"isEmpty\",\r\n  \"isNotEmpty\",\r\n] as const\r\nconst multiSelectOperators = [\r\n  \"inArray\",\r\n  \"notInArray\",\r\n  \"isEmpty\",\r\n  \"isNotEmpty\",\r\n] as const\r\nconst selectOperators = [\"eq\", \"ne\", \"isEmpty\", \"isNotEmpty\"] as const\r\nconst booleanOperators = [\"eq\", \"ne\"] as const\r\n\r\nexport const getDefaultFilterOperator = (filterVariant: FilterVariant) => {\r\n  switch (filterVariant) {\r\n    case \"text\":\r\n      return \"iLike\"\r\n    case \"number\":\r\n      return \"eq\"\r\n    case \"date\":\r\n      return \"eq\"\r\n    case \"boolean\":\r\n      return \"eq\"\r\n    case \"select\":\r\n      return \"eq\"\r\n    case \"multiSelect\":\r\n      return \"inArray\"\r\n    default:\r\n      return \"eq\"\r\n  }\r\n}\r\n\r\nexport const getFilterOperators = (\r\n  filterVariant: FilterVariant,\r\n  dict: ToolbarDictionaries[\"filter\"][\"filterItem\"][\"operatorSelector\"][\"operators\"]\r\n) => {\r\n  switch (filterVariant) {\r\n    case \"text\":\r\n      return textOperators.map((operator) => ({\r\n        value: operator,\r\n        label: dict.text[operator],\r\n      }))\r\n    case \"number\":\r\n    case \"numberRange\":\r\n      return numericOperators.map((operator) => ({\r\n        value: operator,\r\n        label: dict.number[operator],\r\n      }))\r\n    case \"date\":\r\n    case \"dateRange\":\r\n      return dateOperators.map((operator) => ({\r\n        value: operator,\r\n        label: dict.date[operator],\r\n      }))\r\n    case \"boolean\":\r\n      return booleanOperators.map((operator) => ({\r\n        value: operator,\r\n        label: dict.boolean[operator],\r\n      }))\r\n    case \"select\":\r\n      return selectOperators.map((operator) => ({\r\n        value: operator,\r\n        label: dict.select[operator],\r\n      }))\r\n    case \"multiSelect\":\r\n      return multiSelectOperators.map((operator) => ({\r\n        value: operator,\r\n        label: dict.multiSelect[operator],\r\n      }))\r\n  }\r\n}\r\n\r\nconst filterTextStateSchema = z.object({\r\n  id: z.string(),\r\n  value: z.string().min(1),\r\n  variant: z.enum([\"text\"]), // text\r\n  operator: z.enum(textOperators),\r\n  filterId: z.string(),\r\n})\r\n\r\nconst filterNumericStateSchema = filterTextStateSchema.extend({\r\n  variant: z.enum([\"number\"]), // number\r\n  operator: z.enum(numericOperators).exclude([\"isBetween\"]),\r\n  value: z.string().min(1),\r\n})\r\n\r\nconst filterNumericRangeStateSchema = filterTextStateSchema.extend({\r\n  variant: z.enum([\"numberRange\"]), // numberRange\r\n  operator: z.enum([\"isBetween\"]),\r\n  value: z.array(z.string()).min(2),\r\n})\r\n\r\nconst filterDateStateSchema = filterTextStateSchema.extend({\r\n  variant: z.enum([\"date\"]), // date\r\n  operator: z.enum(dateOperators).exclude([\"isBetween\"]),\r\n  value: z.string().min(1),\r\n})\r\n\r\nconst filterDateRangeStateSchema = filterTextStateSchema.extend({\r\n  variant: z.enum([\"dateRange\"]), // dateRange\r\n  operator: z.enum([\"isBetween\"]),\r\n  value: z.array(z.string()).min(2),\r\n})\r\n\r\nconst filterBooleanStateSchema = filterTextStateSchema.extend({\r\n  variant: z.enum([\"boolean\"]), // boolean\r\n  operator: z.enum(booleanOperators),\r\n  value: z.stringbool(),\r\n})\r\n\r\nconst filterSelectStateSchema = filterTextStateSchema.extend({\r\n  variant: z.enum([\"select\"]), // select\r\n  operator: z.enum(selectOperators),\r\n  value: z.string().min(1),\r\n})\r\n\r\nconst filterMultiSelectStateSchema = filterTextStateSchema.extend({\r\n  variant: z.enum([\"multiSelect\"]), // multiSelect\r\n  operator: z.enum(multiSelectOperators),\r\n  value: z.union([z.array(z.string()).min(1), z.enum([\"empty\", \"notEmpty\"])]),\r\n})\r\n\r\nexport const filterJoinStateSchema = z.enum([\"AND\", \"OR\"])\r\n\r\nexport const filterStateSchema = z.discriminatedUnion(\"variant\", [\r\n  filterTextStateSchema,\r\n  filterNumericStateSchema,\r\n  filterNumericRangeStateSchema,\r\n  filterDateStateSchema,\r\n  filterDateRangeStateSchema,\r\n  filterBooleanStateSchema,\r\n  filterSelectStateSchema,\r\n  filterMultiSelectStateSchema,\r\n])\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Filter Parser\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\nexport const getFiltersStateParser = (itemIds?: string[] | Set<string>) => {\r\n  const validKeys = itemIds\r\n    ? itemIds instanceof Set\r\n      ? itemIds\r\n      : new Set(itemIds)\r\n    : null\r\n\r\n  return createParser({\r\n    parse: (value) => {\r\n      try {\r\n        const parsed = JSON.parse(value)\r\n        const result = z.array(filterStateSchema).safeParse(parsed)\r\n\r\n        if (!result.success) return null\r\n\r\n        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {\r\n          return null\r\n        }\r\n\r\n        return result.data\r\n      } catch {\r\n        return null\r\n      }\r\n    },\r\n    serialize: (value) => JSON.stringify(value),\r\n    eq: (a, b) =>\r\n      a.length === b.length &&\r\n      a.every(\r\n        (filter, index) =>\r\n          filter.id === b[index]?.id &&\r\n          filter.value === b[index]?.value &&\r\n          filter.variant === b[index]?.variant &&\r\n          filter.operator === b[index]?.operator\r\n      ),\r\n  })\r\n}\r\n\r\n/**\r\n * parser to prisam query whare query\r\n */\r\nconst textVariantParser = (filter: FilterState & { variant: \"text\" }) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n  const operator = filter.operator\r\n\r\n  switch (operator) {\r\n    case \"iLike\": {\r\n      return { [id]: { contains: value, mode: \"insensitive\" } }\r\n    }\r\n    case \"notILike\": {\r\n      return { NOT: { [id]: { contains: value, mode: \"insensitive\" } } }\r\n    }\r\n    case \"eq\": {\r\n      return { [id]: value }\r\n    }\r\n    case \"ne\": {\r\n      return { NOT: { [id]: value } }\r\n    }\r\n    // case \"isEmpty\": {\r\n    //   return { [id]: null }\r\n    // }\r\n    // case \"isNotEmpty\": {\r\n    //   return { [id]: { not: null } }\r\n    // }\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\nconst numericVariantParser = (filter: FilterState & { variant: \"number\" }) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n  const operator = filter.operator\r\n\r\n  switch (operator) {\r\n    case \"eq\": {\r\n      return { [id]: Number(value) }\r\n    }\r\n    case \"ne\": {\r\n      return { NOT: { [id]: Number(value) } }\r\n    }\r\n    case \"lt\": {\r\n      return { [id]: { lt: Number(value) } }\r\n    }\r\n    case \"lte\": {\r\n      return { [id]: { lte: Number(value) } }\r\n    }\r\n    case \"gt\": {\r\n      return { [id]: { gt: Number(value) } }\r\n    }\r\n    case \"gte\": {\r\n      return { [id]: { gte: Number(value) } }\r\n    }\r\n    // case \"isEmpty\": {\r\n    //   return { [id]: null }\r\n    // }\r\n    // case \"isNotEmpty\": {\r\n    //   return { [id]: { not: null } }\r\n    // }\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\nconst numericRangeVariantParser = (\r\n  filter: FilterState & { variant: \"numberRange\" }\r\n) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n\r\n  return { [id]: { gt: Number(value[0]), lte: Number(value[1]) } }\r\n}\r\n\r\nconst dateVariantParser = (filter: FilterState & { variant: \"date\" }) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n  const operator = filter.operator\r\n  const date = new Date(Number(value))\r\n  const startDay = startOfDay(date)\r\n  const endDay = endOfDay(date)\r\n\r\n  switch (operator) {\r\n    case \"eq\": {\r\n      return { [id]: { gte: startDay, lte: endDay } }\r\n    }\r\n    case \"ne\": {\r\n      return { NOT: { [id]: { gte: startDay, lte: endDay } } }\r\n    }\r\n    case \"lt\": {\r\n      return { [id]: { lt: startDay } }\r\n    }\r\n    case \"lte\": {\r\n      return { [id]: { lte: endDay } }\r\n    }\r\n    case \"gt\": {\r\n      return { [id]: { gt: endDay } }\r\n    }\r\n    case \"gte\": {\r\n      return { [id]: { gte: startDay } }\r\n    }\r\n    // case \"isEmpty\": {\r\n    //   return { [id]: null }\r\n    // }\r\n    // case \"isNotEmpty\": {\r\n    //   return { [id]: { not: null } }\r\n    // }\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\nconst dateRangeVariantParser = (\r\n  filter: FilterState & { variant: \"dateRange\" }\r\n) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n  const min = startOfDay(new Date(Number(value[0])))\r\n  const max = endOfDay(new Date(Number(value[1])))\r\n\r\n  return {\r\n    [id]: { gte: min, lte: max },\r\n  }\r\n}\r\n\r\nconst booleanVariantParser = (filter: FilterState & { variant: \"boolean\" }) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n  const operator = filter.operator\r\n\r\n  switch (operator) {\r\n    case \"eq\": {\r\n      return { [id]: Boolean(value) }\r\n    }\r\n    case \"ne\": {\r\n      return { NOT: { [id]: Boolean(value) } }\r\n    }\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\nconst selectVariantParser = (filter: FilterState & { variant: \"select\" }) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n  const operator = filter.operator\r\n\r\n  switch (operator) {\r\n    case \"eq\": {\r\n      return { [id]: value }\r\n    }\r\n    case \"ne\": {\r\n      return { NOT: { [id]: value } }\r\n    }\r\n    // case \"isEmpty\": {\r\n    //   return { [id]: null }\r\n    // }\r\n    // case \"isNotEmpty\": {\r\n    //   return { [id]: { not: null } }\r\n    // }\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\nconst multiSelectVariantParser = (\r\n  filter: FilterState & { variant: \"multiSelect\" }\r\n) => {\r\n  const id = filter.id\r\n  const value = filter.value\r\n  const operator = filter.operator\r\n\r\n  switch (operator) {\r\n    case \"inArray\": {\r\n      return { [id]: { in: value } }\r\n    }\r\n    case \"notInArray\": {\r\n      return { NOT: { [id]: { in: value } } }\r\n    }\r\n    // case \"isEmpty\": {\r\n    //   return { [id]: null }\r\n    // }\r\n    // case \"isNotEmpty\": {\r\n    //   return { [id]: { not: null } }\r\n    // }\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\nexport const getFilterQueryParser = React.cache(\r\n  ({ filters, join }: { filters: FilterState[]; join: FilterJoinState }) => {\r\n    const parsedFilters = filters\r\n      .map((filter) => {\r\n        switch (filter.variant) {\r\n          case \"text\":\r\n            return textVariantParser(filter)\r\n          case \"number\":\r\n            return numericVariantParser(filter)\r\n          case \"numberRange\":\r\n            return numericRangeVariantParser(filter)\r\n          case \"date\":\r\n            return dateVariantParser(filter)\r\n          case \"dateRange\":\r\n            return dateRangeVariantParser(filter)\r\n          case \"boolean\":\r\n            return booleanVariantParser(filter)\r\n          case \"select\":\r\n            return selectVariantParser(filter)\r\n          case \"multiSelect\":\r\n            return multiSelectVariantParser(filter)\r\n          default:\r\n            return undefined\r\n        }\r\n      })\r\n      .filter(Boolean)\r\n\r\n    return parsedFilters.length > 0 ? { [join]: parsedFilters } : {}\r\n  }\r\n)\r\n", "type": "registry:lib", "target": "src/components/toolbar/lib/toolbar-filter-lib.ts"}, {"path": "registry/new-york/blocks/toolbar/lib/toolbar-search-lib.ts", "content": "import { createParser } from \"nuqs/server\"\r\nimport { z } from \"zod\"\r\n\r\nexport const searchStateSchema = z.union([\r\n  z.object({\r\n    id: z.string().min(1),\r\n    value: z.string().min(1),\r\n  }),\r\n  z.object({\r\n    id: z.undefined(),\r\n    value: z.undefined(),\r\n  }),\r\n])\r\n\r\nexport type SearchState = z.infer<typeof searchStateSchema>\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Search Parser\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\nexport const getSearchStateParser = (itemKey?: string) => {\r\n  return createParser({\r\n    parse: (value) => {\r\n      try {\r\n        const parsed = JSON.parse(value)\r\n        const result = searchStateSchema.safeParse(parsed)\r\n\r\n        if (!result.success) return null\r\n\r\n        if (itemKey && result.data?.id !== itemKey) {\r\n          return null\r\n        }\r\n\r\n        return result.data\r\n      } catch {\r\n        return null\r\n      }\r\n    },\r\n    serialize: (value) => JSON.stringify(value),\r\n    eq: (a, b) => a.id === b.id && a.value === b.value,\r\n  })\r\n}\r\n\r\nexport const getSearchQueryParser = (search: SearchState) => {\r\n  if (!search.id || !search.value) return {} // no search value or id provided, return null to skip search query parsing.\r\n  return { [search.id]: { contains: search.value, mode: \"insensitive\" } }\r\n}\r\n", "type": "registry:lib", "target": "src/components/toolbar/lib/toolbar-search-lib.ts"}, {"path": "registry/new-york/blocks/toolbar/lib/toolbar-sorting-lib.ts", "content": "import { createParser } from \"nuqs/server\"\r\nimport { z } from \"zod/v4\"\r\n\r\nimport { SortingState } from \"../types\"\r\n\r\nexport const sortingOrders = [\"asc\", \"desc\"] as const\r\n\r\nexport const sortingStateSchema = z.object({\r\n  id: z.string().min(1),\r\n  value: z.enum(sortingOrders),\r\n})\r\n\r\nexport const getSortingStateParser = (itemIds?: string[] | Set<string>) => {\r\n  const validKeys = itemIds\r\n    ? itemIds instanceof Set\r\n      ? itemIds\r\n      : new Set(itemIds)\r\n    : null\r\n\r\n  return createParser({\r\n    parse: (value) => {\r\n      try {\r\n        const parsed = JSON.parse(value)\r\n        const result = z.array(sortingStateSchema).safeParse(parsed)\r\n\r\n        if (!result.success) return null\r\n\r\n        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {\r\n          return null\r\n        }\r\n\r\n        return result.data\r\n      } catch {\r\n        return null\r\n      }\r\n    },\r\n    serialize: (value) => JSON.stringify(value),\r\n    eq: (a, b) =>\r\n      a.length === b.length &&\r\n      a.every(\r\n        (sort, index) =>\r\n          sort.id === b[index]?.id && sort.value === b[index]?.value\r\n      ),\r\n  })\r\n}\r\n\r\n//\r\n// --------------------------------------------------------------------------------------\r\n// Sorting Parser\r\n// --------------------------------------------------------------------------------------\r\n//\r\n\r\nexport const getSortingQueryParser = (sorting: SortingState[]) => {\r\n  const parsedSorting = sorting.map((sort) => ({\r\n    [sort.id]: sort.value,\r\n  }))\r\n\r\n  return parsedSorting\r\n}\r\n", "type": "registry:lib", "target": "src/components/toolbar/lib/toolbar-sorting-lib.ts"}, {"path": "registry/new-york/blocks/toolbar/lib/index.ts", "content": "export * from \"./toolbar-filter-lib\"\r\nexport * from \"./toolbar-search-lib\"\r\nexport * from \"./toolbar-sorting-lib\"\r\nexport * from \"./toolbar-lib\"\r\n", "type": "registry:file", "target": "src/components/toolbar/lib/index.ts"}, {"path": "registry/new-york/blocks/toolbar/locales/ar.ts", "content": "const toolbarDictionariesAr = {\r\n  filter: {\r\n    triggerButtonLabel: \"تصفية\",\r\n    popover: {\r\n      title: {\r\n        noFilters: \"لم يتم تطبيق أي تصفية\",\r\n        withFilters: \"التصفية\",\r\n      },\r\n      description: {\r\n        noFilters: \"أضف تصفية لتحسين صفوفك.\",\r\n        withFilters: \"تعديل التصفية لتحسين صفوفك.\",\r\n      },\r\n      buttonLabels: {\r\n        addFilter: \"إضافة تصفية\",\r\n        resetFilters: \"إعادة تعيين التصفية\",\r\n      },\r\n    },\r\n    filterItem: {\r\n      JoinSelector: {\r\n        where: \"أين\",\r\n        AND: \"و\",\r\n        OR: \"أو\",\r\n      },\r\n      itemSelector: {\r\n        searchPlaceholder: \"بحث عن حقول...\",\r\n        noFieldsFound: \"لم يتم العثور على حقول.\",\r\n      },\r\n      operatorSelector: {\r\n        searchPlaceholder: \"بحث عن_operators...\",\r\n        noOperatorsFound: \"لم يتم العثور على_operators.\",\r\n        operators: {\r\n          text: {\r\n            iLike: \"يحتوي\",\r\n            notILike: \"لا يحتوي\",\r\n            eq: \"يساوي\",\r\n            ne: \"لا يساوي\",\r\n            isEmpty: \"فارغ\",\r\n            isNotEmpty: \"غير فارغ\",\r\n          },\r\n          number: {\r\n            eq: \"يساوي\",\r\n            ne: \"لا يساوي\",\r\n            lt: \"أقل من\",\r\n            lte: \"أقل من أو يساوي\",\r\n            gt: \"أكبر من\",\r\n            gte: \"أكبر من أو يساوي\",\r\n            isBetween: \"بين\",\r\n            isEmpty: \"فارغ\",\r\n            isNotEmpty: \"غير فارغ\",\r\n          },\r\n          date: {\r\n            eq: \"يساوي\",\r\n            ne: \"لا يساوي\",\r\n            lt: \"أقل من\",\r\n            lte: \"أقل من أو يساوي\",\r\n            gt: \"أكبر من\",\r\n            gte: \"أكبر من أو يساوي\",\r\n            isBetween: \"بين\",\r\n            isEmpty: \"فارغ\",\r\n            isNotEmpty: \"غير فارغ\",\r\n          },\r\n          boolean: {\r\n            eq: \"يساوي\",\r\n            ne: \"لا يساوي\",\r\n          },\r\n          select: {\r\n            eq: \"يساوي\",\r\n            ne: \"لا يساوي\",\r\n            isEmpty: \"فارغ\",\r\n            isNotEmpty: \"غير فارغ\",\r\n          },\r\n          multiSelect: {\r\n            inArray: \"يحتوي على أي من\",\r\n            notInArray: \"لا يحتوي على أي من\",\r\n            isEmpty: \"فارغ\",\r\n            isNotEmpty: \"غير فارغ\",\r\n          },\r\n        },\r\n      },\r\n      valueFilter: {\r\n        searchPlaceholder: \"بحث عن قيم...\",\r\n        noValuesFound: \"لم يتم العثور على قيم.\",\r\n      },\r\n    },\r\n  },\r\n  sorting: {\r\n    triggerButtonLabel: \"فرز\",\r\n    popover: {\r\n      title: {\r\n        noSorting: \"لم يتم تطبيق أي فرز\",\r\n        withSorting: \"الفرز\",\r\n      },\r\n      description: \"أضف فرز لتنظيم صفوفك.\",\r\n      buttonLabels: {\r\n        addSorting: \"إضافة فرز\",\r\n        resetSorting: \"إعادة تعيين الفرز\",\r\n      },\r\n    },\r\n    sortingItem: {\r\n      fieldSelector: {\r\n        searchPlaceholder: \"بحث عن حقول...\",\r\n        noFieldsFound: \"لم يتم العثور على حقول.\",\r\n      },\r\n      directionSelector: {\r\n        asc: \"تصاعدي\",\r\n        desc: \"تنازلي\",\r\n      },\r\n    },\r\n  },\r\n}\r\n\r\nexport default toolbarDictionariesAr\r\n", "type": "registry:file", "target": "src/components/toolbar/locales/ar.ts"}, {"path": "registry/new-york/blocks/toolbar/locales/en.ts", "content": "const toolbarDictionariesEn = {\r\n  filter: {\r\n    triggerButtonLabel: \"Filter\",\r\n    popover: {\r\n      title: {\r\n        noFilters: \"No filters applied\",\r\n        withFilters: \"Filters\",\r\n      },\r\n      description: {\r\n        noFilters: \"Add filters to refine your rows.\",\r\n        withFilters: \"Modify filters to refine your rows.\",\r\n      },\r\n      buttonLabels: {\r\n        addFilter: \"Add filter\",\r\n        resetFilters: \"Reset filters\",\r\n      },\r\n    },\r\n    filterItem: {\r\n      JoinSelector: {\r\n        where: \"Where\",\r\n        AND: \"AND\",\r\n        OR: \"OR\",\r\n      },\r\n      itemSelector: {\r\n        searchPlaceholder: \"Search fields...\",\r\n        noFieldsFound: \"No fields found.\",\r\n      },\r\n      operatorSelector: {\r\n        searchPlaceholder: \"Search operators...\",\r\n        noOperatorsFound: \"No operators found.\",\r\n        operators: {\r\n          text: {\r\n            iLike: \"Contains\",\r\n            notILike: \"Does not contain\",\r\n            eq: \"Is\",\r\n            ne: \"Is not\",\r\n            isEmpty: \"Is empty\",\r\n            isNotEmpty: \"Is not empty\",\r\n          },\r\n          number: {\r\n            eq: \"Is\",\r\n            ne: \"Is not\",\r\n            lt: \"Is less than\",\r\n            lte: \"Is less than or equal to\",\r\n            gt: \"Is greater than\",\r\n            gte: \"Is greater than or equal to\",\r\n            isBetween: \"Is between\",\r\n            isEmpty: \"Is empty\",\r\n            isNotEmpty: \"Is not empty\",\r\n          },\r\n          date: {\r\n            eq: \"Is\",\r\n            ne: \"Is not\",\r\n            lt: \"Is before\",\r\n            lte: \"Is on or before\",\r\n            gt: \"Is after\",\r\n            gte: \"Is on or after\",\r\n            isBetween: \"Is between\",\r\n            isEmpty: \"Is empty\",\r\n            isNotEmpty: \"Is not empty\",\r\n          },\r\n          boolean: {\r\n            eq: \"Is\",\r\n            ne: \"Is not\",\r\n          },\r\n          select: {\r\n            eq: \"Is\",\r\n            ne: \"Is not\",\r\n            isEmpty: \"Is empty\",\r\n            isNotEmpty: \"Is not empty\",\r\n          },\r\n          multiSelect: {\r\n            inArray: \"Has any of\",\r\n            notInArray: \"Has none of\",\r\n            isEmpty: \"Is empty\",\r\n            isNotEmpty: \"Is not empty\",\r\n          },\r\n        },\r\n      },\r\n      valueFilter: {\r\n        searchPlaceholder: \"Search values...\",\r\n        noValuesFound: \"No values found.\",\r\n      },\r\n    },\r\n  },\r\n  sorting: {\r\n    triggerButtonLabel: \"Sorting\",\r\n    popover: {\r\n      title: {\r\n        noSorting: \"No sorting applied\",\r\n        withSorting: \"Sorting\",\r\n      },\r\n      description: \"Add sorting to organize your rows.\",\r\n      buttonLabels: {\r\n        addSorting: \"Add sorting\",\r\n        resetSorting: \"Reset sorting\",\r\n      },\r\n    },\r\n    sortingItem: {\r\n      fieldSelector: {\r\n        searchPlaceholder: \"Search fields...\",\r\n        noFieldsFound: \"No fields found.\",\r\n      },\r\n      directionSelector: {\r\n        asc: \"Ascending\",\r\n        desc: \"Descending\",\r\n      },\r\n    },\r\n  },\r\n}\r\n\r\nexport default toolbarDictionariesEn\r\n", "type": "registry:file", "target": "src/components/toolbar/locales/en.ts"}, {"path": "registry/new-york/blocks/toolbar/types/toolbar-types.ts", "content": "import type * as React from \"react\"\r\n\r\nimport { Direction, Locale } from \"@/types/globals\"\r\n\r\nimport type { getToolbarDictionary } from \"../lib\"\r\n\r\nexport type ToolbarDictionaries = Awaited<ReturnType<typeof getToolbarDictionary>>\r\n\r\nexport interface ToolbarProps {\r\n  startTransition?: React.TransitionStartFunction\r\n  dir?: Direction\r\n  locale?: Locale\r\n  children?: React.ReactNode\r\n  className?: string\r\n}\r\n\r\nexport interface ToolbarContextValue {\r\n  dictionaries: ToolbarDictionaries\r\n  dir: Direction\r\n  startTransition?: React.TransitionStartFunction\r\n}\r\n", "type": "registry:file", "target": "src/components/toolbar/types/toolbar-types.ts"}, {"path": "registry/new-york/blocks/toolbar/types/toolbar-filter-types.ts", "content": "import React from \"react\"\r\nimport { z } from \"zod/v4\"\r\n\r\nimport { DynamicObject } from \"./../../../../../src/types/globals\"\r\n\r\nimport type { filterJoinStateSchema, filterStateSchema } from \"../lib\"\r\n\r\nexport type FilterVariant =\r\n  | \"text\"\r\n  | \"number\"\r\n  | \"date\"\r\n  | \"boolean\"\r\n  | \"select\"\r\n  | \"multiSelect\"\r\n\r\n  // It is used internally only and is not defined by the ToolbarFilter items properties.\r\n  | \"dateRange\"\r\n  | \"numberRange\"\r\n\r\nexport type Operator = z.infer<typeof filterStateSchema>[\"operator\"]\r\nexport type FilterState = z.infer<typeof filterStateSchema>\r\nexport type FilterJoinState = z.infer<typeof filterJoinStateSchema>\r\n\r\nexport interface BaseFilterItem<K extends string = string> {\r\n  /**\r\n   * Unique identifier for the filter field.\r\n   * Must match a property key in the data object being filtered.\r\n   */\r\n  id: K\r\n\r\n  /**\r\n   * Display label shown to users in the filter interface.\r\n   * Should be localized based on the current locale (Arabic/English).\r\n   */\r\n  label: string\r\n\r\n  /**\r\n   * Placeholder text shown in empty input fields.\r\n   * Should be localized based on the current locale.\r\n   */\r\n  placeholder?: string\r\n}\r\n\r\nexport interface TextFilterItem<K extends string = string>\r\n  extends BaseFilterItem<K> {\r\n  /**\r\n   * Type of filter to render and validation to apply.\r\n   * 'text': String-based filtering with operators like \"contains\", \"equals\"\r\n   */\r\n  variant: \"text\"\r\n}\r\n\r\nexport interface NumberFilterItem<K extends string = string>\r\n  extends BaseFilterItem<K> {\r\n  /**\r\n   * Type of filter to render and validation to apply.\r\n   * 'number': Numeric filtering with operators like \"greater than\", \"less than\"\r\n   */\r\n  variant: \"number\"\r\n\r\n  /**\r\n   * Value range constraints.\r\n   * Defines minimum and maximum allowed values for input.\r\n   */\r\n  range?: { min?: number; max?: number }\r\n\r\n  /**\r\n   * Step increment for number input controls.\r\n   */\r\n  step?: number\r\n\r\n  /**\r\n   * Unit of measurement displayed next to the input field.\r\n   * Example: '$' for currency, 'kg' for weight, etc.\r\n   */\r\n  unit?: string\r\n}\r\n\r\nexport interface DateFilterItem<K extends string = string>\r\n  extends BaseFilterItem<K> {\r\n  /**\r\n   * Type of filter to render and validation to apply.\r\n   * 'date': Date-based filtering with date range support\r\n   */\r\n  variant: \"date\"\r\n\r\n  /**\r\n   * Date range constraints in ISO 8601 format.\r\n   * Defines minimum and maximum allowed dates for input.\r\n   *\r\n   * @see https://en.wikipedia.org/wiki/ISO_8601\r\n   */\r\n  range?: { min?: Date | string; max?: Date | string }\r\n}\r\n\r\nexport interface BooleanFilterItem<K extends string = string>\r\n  extends BaseFilterItem<K> {\r\n  /**\r\n   * Type of filter to render and validation to apply.\r\n   * 'boolean': True/false filtering\r\n   */\r\n  variant: \"boolean\"\r\n}\r\n\r\nexport interface SelectFilterItem<K extends string = string, V = unknown>\r\n  extends BaseFilterItem<K> {\r\n  /**\r\n   * Type of filter to render and validation to apply.\r\n   * 'select': Single selection from predefined options\r\n   * 'multiSelect': Multiple selection from predefined options\r\n   */\r\n  variant: \"select\" | \"multiSelect\"\r\n\r\n  /**\r\n   * Available options for selection.\r\n   */\r\n  options: Array<{\r\n    /**\r\n     * Display text shown to users for this option.\r\n     */\r\n    label: string\r\n\r\n    /**\r\n     * Actual value used for filtering operations.\r\n     */\r\n    value: V\r\n\r\n    /**\r\n     * Optional count of items associated with this option.\r\n     * Displayed next to the option label in the interface.\r\n     */\r\n    count?: number\r\n\r\n    /**\r\n     * Optional React component rendered as an icon next to the option.\r\n     * Component must accept a className prop.\r\n     */\r\n    icon?: React.ComponentType<{ className?: string; [key: string]: any }>\r\n  }>\r\n}\r\n\r\nexport type FilterItem<T extends DynamicObject = DynamicObject> = {\r\n  [K in keyof T]:\r\n    | TextFilterItem<Extract<K, string>>\r\n    | NumberFilterItem<Extract<K, string>>\r\n    | DateFilterItem<Extract<K, string>>\r\n    | BooleanFilterItem<Extract<K, string>>\r\n    | SelectFilterItem<Extract<K, string>, T[K]>\r\n}[keyof T]\r\n\r\nexport type ToolbarFilterProps<T extends DynamicObject = DynamicObject> = {\r\n  /**\r\n   * قائمة بيانات العناصر التي سيتم استخداها للتصفية.\r\n   */\r\n  items: FilterItem<T>[]\r\n  /**\r\n   * إذا كانت القيمة true، سيتم تحديث عنوان الرابط (URL) باستخدام التصفية\r\n   * دون إعادة تحميل الصفحة (shallow routing).\r\n   *\r\n   * @default false\r\n   */\r\n  shallow?: boolean\r\n  /**\r\n   * مدة التأخير (بالملي ثانية) قبل تطبيق الفلتر وتحديث القيمة في عنوان الرابط (URL).\r\n   * يُستخدم هذا لتقليل عدد مرات التحديث عند الكتابة أو التفاعل مع الفلتر.\r\n   *\r\n   * @default 300\r\n   */\r\n  debounceMs?: number\r\n  /**\r\n   * يتم تطبيق الخصائص على عنصر الزر الخاص بفتح نافذة القائمة فقط.\r\n   */\r\n  className?: string\r\n}\r\n\r\nexport interface FilterContextValue {\r\n  items: FilterItem[]\r\n  shallow: boolean\r\n  debounceMs: number\r\n}\r\n", "type": "registry:file", "target": "src/components/toolbar/types/toolbar-filter-types.ts"}, {"path": "registry/new-york/blocks/toolbar/types/toolbar-sorting-types.ts", "content": "import { z } from \"zod/v4\"\r\n\r\nimport { DynamicObject } from \"@/types/globals\"\r\n\r\nimport type { sortingStateSchema } from \"../lib\"\r\n\r\nexport type SortingState = z.infer<typeof sortingStateSchema>\r\nexport type SortDirection = SortingState[\"value\"] // \"asc\" | \"desc\"\r\n\r\ntype SortingItem<T extends DynamicObject = DynamicObject> = {\r\n  /** مفتاح كائن البيانات الذي تريد تمكين استخدامه للفرز */\r\n  id: Extract<keyof T, string>\r\n  /** العنوان الذي سيظهر في القائمة */\r\n  label: string\r\n}\r\n\r\nexport type OnSortRemove = (sortId: string) => void\r\nexport type OnSortUpdate = (\r\n  sortId: string,\r\n  updates: Partial<SortingState>\r\n) => void\r\n\r\nexport interface SortingItemProps {\r\n  sort: SortingState\r\n  sortItemId: string\r\n  items: { id: string; label: string }[]\r\n  updateSort: OnSortUpdate\r\n  removeSort: OnSortRemove\r\n}\r\n\r\nexport type ToolbarSortingProps<T extends DynamicObject = DynamicObject> = {\r\n  /**\r\n   * مصفوفة العناصر التي تريد تمكين استخدامها للفرز.\r\n   */\r\n  items: SortingItem<T>[]\r\n  className?: string\r\n}\r\n", "type": "registry:file", "target": "src/components/toolbar/types/toolbar-sorting-types.ts"}, {"path": "registry/new-york/blocks/toolbar/types/index.ts", "content": "export * from \"./toolbar-filter-types\"\r\nexport * from \"./toolbar-sorting-types\"\r\nexport * from \"./toolbar-types\"\r\n", "type": "registry:file", "target": "src/components/toolbar/types/index.ts"}, {"path": "registry/new-york/blocks/toolbar/hooks/use-toolbar.ts", "content": "import React from \"react\"\r\n\r\nimport { ToolbarContext } from \"../contexts\"\r\n\r\nexport function useToolbar() {\r\n  const context = React.useContext(ToolbarContext)\r\n  if (!context) {\r\n    throw new Error(\"useToolbarContext must be used within a ToolbarProvider\")\r\n  }\r\n  return context\r\n}\r\n", "type": "registry:hook", "target": "src/components/toolbar/hooks/use-toolbar.ts"}, {"path": "registry/new-york/blocks/toolbar/hooks/use-filter-state.ts", "content": "import * as React from \"react\"\r\nimport { parseAsStringEnum, useQueryState } from \"nuqs\"\r\n\r\nimport { generateId } from \"@/lib/id\"\r\nimport { useDebouncedCallback } from \"@/hooks/use-debounced-callback\"\r\n\r\nimport { FilterContext } from \"../contexts\"\r\nimport { getDefaultFilterOperator, getFiltersStateParser } from \"../lib\"\r\nimport { FilterState } from \"../types\"\r\nimport { useToolbar } from \"./use-toolbar\"\r\n\r\n/**\r\n * @private\r\n * @internal\r\n */\r\nexport function useFilterState() {\r\n  const context = React.useContext(FilterContext)\r\n  if (!context) throw new Error(\"Error From useFilterState\")\r\n\r\n  const { dictionaries, dir, startTransition } = useToolbar()\r\n  const { items, shallow, debounceMs } = context\r\n\r\n  const [filters, setFilters] = useQueryState(\r\n    \"filters\",\r\n    getFiltersStateParser(items.map((item) => item.id))\r\n      .withDefault([])\r\n      .withOptions({\r\n        startTransition,\r\n        clearOnDefault: true,\r\n        throttleMs: debounceMs,\r\n        shallow,\r\n      })\r\n  )\r\n\r\n  const [joinOperator, setJoinOperator] = useQueryState(\r\n    \"join\",\r\n    parseAsStringEnum([\"AND\", \"OR\"]).withDefault(\"AND\").withOptions({\r\n      startTransition,\r\n      clearOnDefault: true,\r\n      shallow: false,\r\n    })\r\n  )\r\n\r\n  const addFilter = React.useCallback(() => {\r\n    const item = items[0]\r\n    if (!item) return\r\n    setFilters((prev) => [\r\n      ...prev,\r\n      {\r\n        id: item.id,\r\n        value: \"\",\r\n        variant: item.variant,\r\n        operator: getDefaultFilterOperator(item.variant),\r\n        filterId: generateId({ length: 8 }),\r\n      } as FilterState,\r\n    ])\r\n  }, [items, setFilters])\r\n\r\n  const debouncedSetFilters = useDebouncedCallback(setFilters, debounceMs)\r\n\r\n  const updateFilter = React.useCallback(\r\n    (filterId: string, updates: Partial<Omit<FilterState, \"filterId\">>) => {\r\n      debouncedSetFilters(\r\n        (prevFilters) =>\r\n          prevFilters.map((filter) => {\r\n            if (filter.filterId === filterId) {\r\n              return { ...filter, ...updates }\r\n            }\r\n            return filter\r\n          }) as FilterState[]\r\n      )\r\n    },\r\n    [debouncedSetFilters]\r\n  )\r\n\r\n  const removeFilter: (filterId: string) => void = React.useCallback(\r\n    (filterId) => {\r\n      setFilters((prevFilters) =>\r\n        prevFilters.filter((filter) => filter.filterId !== filterId)\r\n      )\r\n    },\r\n    [setFilters]\r\n  )\r\n\r\n  const resetFilters = React.useCallback(() => {\r\n    setJoinOperator(\"AND\")\r\n    setFilters([])\r\n  }, [setFilters, setJoinOperator])\r\n\r\n  return {\r\n    dir,\r\n    items,\r\n    filters,\r\n    addFilter,\r\n    setFilters,\r\n    joinOperator,\r\n    setJoinOperator,\r\n    updateFilter,\r\n    removeFilter,\r\n    resetFilters,\r\n    dictionaries: dictionaries.filter,\r\n  }\r\n}\r\n", "type": "registry:hook", "target": "src/components/toolbar/hooks/use-filter-state.ts"}, {"path": "registry/new-york/blocks/toolbar/hooks/index.ts", "content": "export * from \"./use-filter-state\"\r\nexport * from \"./use-toolbar\"\r\n", "type": "registry:file", "target": "src/components/toolbar/hooks/index.ts"}, {"path": "registry/new-york/blocks/toolbar/contexts/create-contexts.ts", "content": "\"use client\"\r\n\r\nimport { createContext } from \"react\"\r\n\r\nimport { FilterContextValue, ToolbarContextValue } from \"../types\"\r\n\r\nexport const FilterContext = createContext<FilterContextValue | null>(null)\r\n\r\nexport const ToolbarContext = createContext<ToolbarContextValue | null>(null)\r\n", "type": "registry:file", "target": "src/components/toolbar/contexts/create-contexts.ts"}, {"path": "registry/new-york/blocks/toolbar/contexts/index.ts", "content": "export * from \"./create-contexts\"\r\n", "type": "registry:file", "target": "src/components/toolbar/contexts/index.ts"}]}