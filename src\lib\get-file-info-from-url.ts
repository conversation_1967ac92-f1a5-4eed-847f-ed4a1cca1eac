/**
 * Retrieves file information (name, size, and type) from a given file URL by making a HEAD request.
 *
 * @param fileUrl - The URL of the file to retrieve information from.
 * @returns An object containing the file's name, size (in bytes, if available), and MIME type (if available).
 *          If the request fails or required headers are missing, `size` and `type` will be `undefined`.
 *
 * @remarks
 * - If the server does not support HEAD requests or does not include CORS headers, the function will return
 *   `undefined` for `size` and `type`, but will always attempt to extract the file name from the URL.
 * - Network errors and HTTP errors are handled gracefully, with errors logged to the console.
 *
 * @example
 * ```typescript
 * const info = await getFileInfoFromUrl('https://example.com/file.pdf');
 * // info: { fileName: 'file.pdf', size: 12345, type: 'application/pdf' }
 * ```
 */
async function getFileInfoFromUrl(fileUrl: string) {
  const segments = fileUrl.split("/")
  const getFileNameFromUrl = segments[segments.length - 1].split("?")[0]

  try {
    let response
    try {
      response = await fetch(fileUrl, { method: "HEAD" })
    } catch (networkError) {
      console.error(
        `Network error occurred while fetching ${fileUrl}:`,
        networkError
      )
      throw new Error(`Network error: Unable to fetch ${fileUrl}`)
    }

    if (!response.ok) {
      throw new Error(`Failed to fetch headers: ${response.status}`)
    }
    const contentType = response.headers.get("content-type") || "unknown"
    const contentLength = response.headers.get("content-length")

    const lengthIsNaN = contentLength && !isNaN(Number(contentLength))

    return {
      size: lengthIsNaN ? parseInt(contentLength, 10) : undefined,
      type: contentType ?? undefined,
      fileName: getFileNameFromUrl,
    }

    // eslint-disable-next-line
  } catch (error) {
    console.log(
      `Unable to access file information from this source ${fileUrl}. This issue might be caused by the source not allowing HEAD requests or the response not including the Access-Control-Allow-Origin header.`
    )

    return {
      type: undefined,
      size: undefined,
      fileName: getFileNameFromUrl,
    }
  }
}

export { getFileInfoFromUrl }
