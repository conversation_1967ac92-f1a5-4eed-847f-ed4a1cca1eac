import { createParser } from "nuqs/server"
import { z } from "zod"

export const searchStateSchema = z.union([
  z.object({
    id: z.string().min(1),
    value: z.string().min(1),
  }),
  z.object({
    id: z.undefined(),
    value: z.undefined(),
  }),
])

export type SearchState = z.infer<typeof searchStateSchema>

//
// --------------------------------------------------------------------------------------
// Search Parser
// --------------------------------------------------------------------------------------
//

export const getSearchStateParser = (itemKey?: string) => {
  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = searchStateSchema.safeParse(parsed)

        if (!result.success) return null

        if (itemKey && result.data?.id !== itemKey) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) => a.id === b.id && a.value === b.value,
  })
}

export const getSearchQueryParser = (search: SearchState) => {
  if (!search.id || !search.value) return {} // no search value or id provided, return null to skip search query parsing.
  return { [search.id]: { contains: search.value, mode: "insensitive" } }
}
