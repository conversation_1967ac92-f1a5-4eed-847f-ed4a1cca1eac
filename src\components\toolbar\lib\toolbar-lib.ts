import React from "react"
import {
  createSearchParamsCache,
  parseAsStringEnum,
  SearchParams,
} from "nuqs/server"

import { Locale } from "@/types/globals"

import toolbarDictionariesAr from "../locales/ar"
import toolbarDictionariesEn from "../locales/en"
import {
  getFilterQueryParser,
  getFiltersStateParser,
} from "./toolbar-filter-lib"
import {
  getSearchQueryParser,
  getSearchStateParser,
} from "./toolbar-search-lib"
import {
  getSortingQueryParser,
  getSortingStateParser,
} from "./toolbar-sorting-lib"

export type GetToolbarDictionaryResult = ReturnType<typeof getToolbarDictionary>

const toolbarDictionaries = {
  en: toolbarDictionariesEn,
  ar: toolbarDictionariesAr,
}

export const getToolbarDictionary = (locale: Locale) => {
  return toolbarDictionaries[locale]
}

export const getToolbarQueryParser = React.cache(
  async (searchParams: Promise<SearchParams> | SearchParams) => {
    const searchParamsCache = createSearchParamsCache({
      filters: getFiltersStateParser().withDefault([]),
      join: parseAsStringEnum(["AND", "OR"]).withDefault("AND"),
      search: getSearchStateParser().withDefault({}),
      sort: getSortingStateParser().withDefault([]),
    })

    const searchParamsCacheParsed = searchParamsCache.parse(await searchParams)

    const filters = getFilterQueryParser({
      filters: searchParamsCacheParsed.filters,
      join: searchParamsCacheParsed.join,
    })

    const search = getSearchQueryParser(searchParamsCacheParsed.search)
    const sorting = getSortingQueryParser(searchParamsCacheParsed.sort)

    const where = { ...filters, ...search }
    const orderBy = sorting

    return { where, orderBy }
  }
)
