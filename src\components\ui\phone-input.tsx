import * as React from "react"
import { <PERSON><PERSON><PERSON>, ChevronsUpDown } from "lucide-react"
import * as RPNInput from "react-phone-number-input"
import flags from "react-phone-number-input/flags"
import ar from "react-phone-number-input/locale/ar"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import { ScrollArea } from "./scroll-area"

const PhoneInput = React.memo(
  ({
    className,
    onChange,
    ...props
  }: React.ComponentProps<typeof RPNInput.default>) => {
    const handleChange = React.useCallback(
      (value: RPNInput.Value | undefined) => {
        if (value) onChange?.(value)
      },
      [onChange]
    )

    return (
      <RPNInput.default
        labels={ar}
        className={cn("flex", className)}
        flagComponent={FlagComponent}
        countrySelectComponent={CountrySelect}
        inputComponent={InputComponent}
        smartCaret={false}
        onChange={handleChange}
        {...props}
      />
    )
  }
)

PhoneInput.displayName = "PhoneInput"

const InputComponent = React.memo(
  ({ className, ...props }: React.ComponentProps<"input">) => (
    <Input
      className={cn(
        "ltr:rounded-s-none ltr:rounded-e-md rtl:rounded-s-md rtl:rounded-e-none rtl:text-right!",
        className
      )}
      type="tel"
      autoComplete="tel"
      {...props}
    />
  )
)

InputComponent.displayName = "InputComponent"

type CountrySelectOption = { label: string; value: RPNInput.Country }

type CountrySelectProps = {
  disabled?: boolean
  value: RPNInput.Country
  onChange: (value: RPNInput.Country) => void
  options: CountrySelectOption[]
}

const CountrySelect = React.memo(
  ({ disabled, value, onChange, options }: CountrySelectProps) => {
    //
    const scrollAreaRef = React.useRef<HTMLDivElement>(null)
    const handleSelect = React.useCallback(
      (country: RPNInput.Country) => {
        onChange(country)
      },
      [onChange]
    )

    const filteredOptions = React.useMemo(() => {
      return options.filter((x) => x.value)
    }, [options])

    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            type="button"
            variant={"outline"}
            className="flex gap-1 rounded-e-none border-r-0 px-3 focus:z-10 rtl:flex-row-reverse"
            disabled={disabled}
          >
            <FlagComponent country={value} countryName={value} />
            <ChevronsUpDown
              className={cn(
                "h-4 w-4 opacity-50 ltr:-mr-2",
                disabled ? "hidden" : "opacity-100"
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          sticky="always"
          avoidCollisions
          className="w-[300px] p-0"
        >
          <Command>
            <CommandInput
              placeholder="Search country..."
              onValueChange={() => {
                setTimeout(() => {
                  if (scrollAreaRef.current) {
                    const viewportElement = scrollAreaRef.current.querySelector(
                      "[data-radix-scroll-area-viewport]"
                    )
                    if (viewportElement) {
                      viewportElement.scrollTop = 0
                    }
                  }
                }, 50)
              }}
            />
            <CommandList>
              <ScrollArea
                ref={scrollAreaRef}
                dir="rtl"
                className="h-72 max-h-[calc(50vh-70px)]"
              >
                <CommandEmpty>No country found.</CommandEmpty>
                <CommandGroup>
                  {filteredOptions.map((option) => (
                    <CountrySelectItem
                      key={option.value}
                      option={option}
                      value={value}
                      handleSelect={handleSelect}
                    />
                  ))}
                </CommandGroup>
              </ScrollArea>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    )
  }
)

CountrySelect.displayName = "CountrySelect"

type CountrySelectItemProps = {
  option: CountrySelectOption
  value: RPNInput.Country
  handleSelect: (country: RPNInput.Country) => void
}

const CountrySelectItem = React.memo(
  ({ option, value, handleSelect }: CountrySelectItemProps) => (
    <CommandItem className="gap-2" onSelect={() => handleSelect(option.value)}>
      <FlagComponent country={option.value} countryName={option.label} />
      <span className="flex-1 text-sm">{option.label}</span>
      {option.value && (
        <span className="text-foreground/50 text-sm" aria-hidden="true">
          {`+${RPNInput.getCountryCallingCode(option.value)}`}
        </span>
      )}
      <CheckIcon
        className={cn(
          "ml-auto h-4 w-4",
          option.value === value ? "opacity-100" : "opacity-0"
        )}
      />
    </CommandItem>
  )
)

CountrySelectItem.displayName = "CountrySelectItem"

const FlagComponent = React.memo(
  ({ country, countryName }: RPNInput.FlagProps) => {
    const Flag = flags[country]

    return (
      <span className="bg-foreground/20 flex h-4 w-6 overflow-hidden rounded-xs [&_svg]:h-fit! [&_svg]:w-6!">
        {Flag && <Flag title={countryName} />}
      </span>
    )
  }
)
FlagComponent.displayName = "FlagComponent"

export { PhoneInput }
