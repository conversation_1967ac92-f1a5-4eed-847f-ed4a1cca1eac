import * as React from "react"
import { <PERSON><PERSON><PERSON><PERSON>, TouchSensor, useSensor, useSensors } from "@dnd-kit/core"
import { RefreshCcw } from "lucide-react"
import { useFormContext } from "react-hook-form"

import { DataFormFieldProps } from "@/types/data-form"

import { Button } from "../ui/button"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useFormField,
} from "../ui/form"
import { Skeleton } from "../ui/skeleton"
import { Sortable, SortableContent, SortableItem } from "../ui/sortable"
import {
  TagsInput,
  TagsInputClear,
  TagsInputInput,
  TagsInputItem,
  TagsInputList,
} from "../ui/tags-input"
import DataFormFieldContainer from "./data-form-field-container"

interface DataFormFieldTagsInputProps<T = string>
  extends DataFormFieldProps<T> {
  /**
   * عندما يكون صحيح, يظهر زر تحت الحقل وضيفة الزر هي حذف كل القيم التي تم ادخالها في الحقل
   * @default false
   */
  enableButtonClearAllTags?: boolean
  /**
   * عندما يكون صحيح, يمكن تعديل العلامات التي تم إدخالها عن طريق النقر نقرتين على العلامة ثم إستبدال قيمة العلامة بالقيمة الجديدة التي سيتم إدخالها
   * @default true
   */
  editable?: boolean
  /**
   * حرف يستخدم لتقسيم النص الملصق إلى علامات متعددة.
   * ملاحضة: لن يكون بالإمكان استخدام الحرف المحدد في قيم العلامات المدخلة.
   *@default ","
   */
  delimiter?: string
  /**
   * الحد الأقصى المسموح به لعدد العلامات
   */
  max?: number
  /**
   * عندما يكون صحيح, يتم عرض الحقل بشكل مرن.
   * بمعنى انه سيتم التحقق مما اذا كان هناك حقل قبل او بعد هذا العنصر إذا وجد الحقل سيتم عرض الحقلين في صف واحد
   * ملاحضة: سيتم عرض كل حقل في صف منفصل عندما تكون المساحة اقل من () حتا اذا كانت هذا الخاصية صحيحة وهذا يضمن عرض الحقول بشكل غير مزدحم في المساحات والشاشات الصغيرة
   * @default true
   */
  autoInline?: boolean
}

/**
 * يتعامل حقل العلامات للتعامل مع قيم المصفوفات التي لا يمكن معرفة قيمها او خياراتها مسبقاً, وهذا يعني أن كل مايعرف عن القيمة هوا انها مصفوفة نصية
 */
const DataFormFieldTagsInput = (props: DataFormFieldTagsInputProps) => {
  const { formItemId } = useFormField()
  const { control } = useFormContext()

  const {
    label,
    name,
    description,
    disabled,
    placeholder,
    required,
    delimiter,
    editable = true,
    max,
    enableButtonClearAllTags,
    autoInline = true,
  } = props

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: { distance: 8 },
    }),
    useSensor(TouchSensor, {
      activationConstraint: { delay: 250, tolerance: 5 },
    })
  )

  return (
    <FormField
      key={name}
      name={name}
      control={control}
      render={({ field }) => (
        <DataFormFieldContainer variant="tagsInput" autoInline={autoInline}>
          <FormItem className="col-span-2 w-full">
            <FormLabel>
              {label}
              {required && <span className="text-destructive"> *</span>}
            </FormLabel>
            <FormControl>
              <Sortable
                sensors={sensors}
                value={field.value ?? []}
                onValueChange={(items) => field.onChange(items)}
                orientation="mixed"
                flatCursor
              >
                <TagsInput
                  max={max}
                  ref={field.ref}
                  className="w-full"
                  value={field.value ?? []}
                  editable={editable}
                  delimiter={delimiter}
                  disabled={disabled ?? field.disabled}
                  onValueChange={(value) => field.onChange(value)}
                >
                  <TagsInputList>
                    <SortableContent>
                      <div className="flex min-h-[30px] w-full flex-wrap items-center gap-1.5 px-3">
                        {(field?.value ?? [])?.length > 0 ? (
                          field?.value?.map((val: string) => (
                            <SortableItem
                              key={val}
                              value={val}
                              // to prevent tag item from being tabbable
                              tabIndex={-1}
                              asChild
                              asHandle
                            >
                              <TagsInputItem value={val}>{val}</TagsInputItem>
                            </SortableItem>
                          ))
                        ) : (
                          <span className="text-muted-foreground/80">
                            لم يتم تحديد قيم
                          </span>
                        )}
                      </div>
                    </SortableContent>
                    <TagsInputInput
                      ref={field.ref}
                      id={formItemId}
                      className="border-t px-3 pt-2"
                      placeholder={placeholder}
                    />
                  </TagsInputList>
                  {enableButtonClearAllTags && (
                    <TagsInputClear asChild>
                      <Button
                        disabled={disabled ?? field.disabled}
                        variant="outline"
                      >
                        <RefreshCcw className="h-4 w-4" />
                        Clear
                      </Button>
                    </TagsInputClear>
                  )}
                </TagsInput>
              </Sortable>
            </FormControl>
            <FormDescription>{description}</FormDescription>
            <FormMessage />
          </FormItem>
        </DataFormFieldContainer>
      )}
    />
  )
}

const DataFormFieldTagsInputSkeleton = () => {
  return (
    <div className="col-span-2 flex min-h-[121px] w-full flex-col gap-2">
      <Skeleton className="h-[14px] w-30 max-w-[50%]" />
      <Skeleton className="h-[91px] w-full" />
    </div>
  )
}

export default DataFormFieldTagsInput
export { type DataFormFieldTagsInputProps, DataFormFieldTagsInputSkeleton }
