"use client"

import * as React from "react"
import { ArrowDownUp, ChevronsUpDown, GripVertical, Trash2 } from "lucide-react"
import { useQueryState } from "nuqs"

import { DynamicObject } from "@/types/globals"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Sortable,
  SortableContent,
  SortableItem,
  SortableItemHandle,
} from "@/components/ui/sortable"

import { useToolbar } from "./hooks"
import { getSortingStateParser, sortingOrders } from "./lib"
import {
  OnSortRemove,
  OnSortUpdate,
  SortDirection,
  SortingItemProps,
  ToolbarSortingProps,
} from "./types"

export function ToolbarSorting<T extends DynamicObject = DynamicObject>({
  items,
  className,
}: ToolbarSortingProps<T>) {
  const id = React.useId()
  const labelId = React.useId()
  const descriptionId = React.useId()
  const addButtonRef = React.useRef<HTMLButtonElement>(null)
  const { startTransition, dictionaries, dir } = useToolbar()
  const dict = dictionaries.sorting

  const [sorting, setSorting] = useQueryState(
    "sort",
    getSortingStateParser(items.map((item) => item.id))
      .withDefault([])
      .withOptions({ clearOnDefault: true, shallow: false, startTransition })
  )

  const addSort = React.useCallback(() => {
    const firstItem = items[0]
    if (!firstItem) return

    setSorting((prevSorting) => [
      ...prevSorting,
      { id: firstItem.id, value: "asc" },
    ])
  }, [setSorting, items])

  const updateSort: OnSortUpdate = React.useCallback(
    (sortId, updates) => {
      setSorting((prevSorting) => {
        if (!prevSorting) return prevSorting
        return prevSorting.map((sort) =>
          sort.id === sortId ? { ...sort, ...updates } : sort
        )
      })
    },
    [setSorting]
  )

  const removeSort: OnSortRemove = React.useCallback(
    (sortId) => {
      setSorting((prevSorting) =>
        prevSorting.filter((item) => item.id !== sortId)
      )
    },
    [setSorting]
  )

  const resetSorting = React.useCallback(() => setSorting([]), [setSorting])

  return (
    <Sortable
      value={sorting}
      onValueChange={setSorting}
      getItemValue={(item) => item.id}
    >
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className={className}>
            <ArrowDownUp />
            {dict.triggerButtonLabel}
            {sorting.length > 0 && (
              <Badge
                variant="secondary"
                className="h-[18.24px] rounded-[3.2px] px-[5.12px] font-mono text-[10.4px] font-normal"
              >
                {sorting.length}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          dir={dir}
          aria-labelledby={labelId}
          aria-describedby={descriptionId}
          className="flex w-full max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-3.5 p-4 sm:min-w-[380px]"
        >
          <div className="flex flex-col gap-1">
            <h4 id={labelId} className="leading-none font-medium">
              {sorting.length > 0
                ? dict.popover.title.withSorting
                : dict.popover.title.noSorting}
            </h4>
            <p
              id={descriptionId}
              className={cn(
                "text-muted-foreground text-sm",
                sorting.length > 0 && "sr-only"
              )}
            >
              {dict.popover.description}
            </p>
          </div>
          {sorting.length > 0 && (
            <SortableContent asChild>
              <div
                role="list"
                className="flex max-h-[300px] flex-col gap-2 overflow-y-auto p-1"
              >
                {sorting.map((sort) => (
                  <SortingItem
                    key={sort.id}
                    sort={sort}
                    sortItemId={`${id}-sort-${sort.id}`}
                    items={items}
                    updateSort={updateSort}
                    removeSort={removeSort}
                  />
                ))}
              </div>
            </SortableContent>
          )}
          <div className="flex w-full items-center gap-2">
            <Button
              size="sm"
              className="rounded"
              ref={addButtonRef}
              onClick={addSort}
              disabled={items.length === 0}
            >
              {dict.popover.buttonLabels.addSorting}
            </Button>
            {sorting.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="rounded"
                onClick={resetSorting}
              >
                {dict.popover.buttonLabels.resetSorting}
              </Button>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </Sortable>
  )
}

//
// --------------------------------------------------------------------------------------
// Sorting Item
// --------------------------------------------------------------------------------------
//

function SortingItem({
  sort,
  items,
  sortItemId,
  updateSort,
  removeSort,
}: SortingItemProps) {
  const [open, setOpen] = React.useState(false)
  const { dictionaries, dir } = useToolbar()

  const fieldListboxId = `${sortItemId}-field-listbox`
  const fieldTriggerId = `${sortItemId}-field-trigger`
  const directionListboxId = `${sortItemId}-direction-listbox`

  const dict = dictionaries.sorting.sortingItem

  return (
    <SortableItem value={sort.id} asChild>
      <div
        role="listitem"
        id={sortItemId}
        tabIndex={-1}
        className="flex items-center gap-2"
      >
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              id={fieldTriggerId}
              role="combobox"
              aria-controls={fieldListboxId}
              variant="outline"
              size="sm"
              className="w-44 justify-between rounded font-normal"
            >
              <span className="truncate">
                {items.find((item) => item.id === sort.id)?.label}
              </span>
              <ChevronsUpDown className="opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            dir={dir}
            id={fieldListboxId}
            className="w-[var(--radix-popover-trigger-width)] origin-[var(--radix-popover-content-transform-origin)] p-0"
          >
            <Command dir={dir}>
              <CommandInput
                placeholder={dict.fieldSelector.searchPlaceholder}
              />
              <CommandList>
                <CommandEmpty>{dict.fieldSelector.noFieldsFound}</CommandEmpty>
                <CommandGroup>
                  {items.map((column) => (
                    <CommandItem
                      key={column.id}
                      value={column.id}
                      onSelect={(value) =>
                        updateSort(sort.id, {
                          id: value,
                        })
                      }
                    >
                      <span className="truncate">{column.label}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <Select
          dir={dir}
          value={sort.value}
          onValueChange={(value: SortDirection) =>
            updateSort(sort.id, { value: value })
          }
        >
          <SelectTrigger
            aria-controls={directionListboxId}
            className="h-8 w-24 rounded [&[data-size]]:h-8"
          >
            <SelectValue />
          </SelectTrigger>
          <SelectContent
            id={directionListboxId}
            className="min-w-[var(--radix-select-trigger-width)] origin-[var(--radix-select-content-transform-origin)]"
          >
            {sortingOrders.map((order: SortDirection) => (
              <SelectItem key={order} value={order}>
                {dict.directionSelector[order]}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          aria-controls={sortItemId}
          variant="outline"
          size="icon"
          className="size-8 shrink-0 rounded"
          onClick={() => removeSort(sort.id)}
        >
          <Trash2 />
        </Button>
        <SortableItemHandle asChild>
          <Button
            variant="outline"
            size="icon"
            className="size-8 shrink-0 rounded"
          >
            <GripVertical />
          </Button>
        </SortableItemHandle>
      </div>
    </SortableItem>
  )
}
