const toolbarDictionariesAr = {
  filter: {
    triggerButtonLabel: "تصفية",
    popover: {
      title: {
        noFilters: "لم يتم تطبيق أي تصفية",
        withFilters: "التصفية",
      },
      description: {
        noFilters: "أضف تصفية لتحسين صفوفك.",
        withFilters: "تعديل التصفية لتحسين صفوفك.",
      },
      buttonLabels: {
        addFilter: "إضافة تصفية",
        resetFilters: "إعادة تعيين التصفية",
      },
    },
    filterItem: {
      JoinSelector: {
        where: "أين",
        AND: "و",
        OR: "أو",
      },
      itemSelector: {
        searchPlaceholder: "بحث عن حقول...",
        noFieldsFound: "لم يتم العثور على حقول.",
      },
      operatorSelector: {
        searchPlaceholder: "بحث عن_operators...",
        noOperatorsFound: "لم يتم العثور على_operators.",
        operators: {
          text: {
            iLike: "يحتوي",
            notILike: "لا يحتوي",
            eq: "يساوي",
            ne: "لا يساوي",
            isEmpty: "فارغ",
            isNotEmpty: "غير فارغ",
          },
          number: {
            eq: "يساوي",
            ne: "لا يساوي",
            lt: "أقل من",
            lte: "أقل من أو يساوي",
            gt: "أكبر من",
            gte: "أكبر من أو يساوي",
            isBetween: "بين",
            isEmpty: "فارغ",
            isNotEmpty: "غير فارغ",
          },
          date: {
            eq: "يساوي",
            ne: "لا يساوي",
            lt: "أقل من",
            lte: "أقل من أو يساوي",
            gt: "أكبر من",
            gte: "أكبر من أو يساوي",
            isBetween: "بين",
            isEmpty: "فارغ",
            isNotEmpty: "غير فارغ",
          },
          boolean: {
            eq: "يساوي",
            ne: "لا يساوي",
          },
          select: {
            eq: "يساوي",
            ne: "لا يساوي",
            isEmpty: "فارغ",
            isNotEmpty: "غير فارغ",
          },
          multiSelect: {
            inArray: "يحتوي على أي من",
            notInArray: "لا يحتوي على أي من",
            isEmpty: "فارغ",
            isNotEmpty: "غير فارغ",
          },
        },
      },
      valueFilter: {
        searchPlaceholder: "بحث عن قيم...",
        noValuesFound: "لم يتم العثور على قيم.",
      },
    },
  },
  sorting: {
    triggerButtonLabel: "فرز",
    popover: {
      title: {
        noSorting: "لم يتم تطبيق أي فرز",
        withSorting: "الفرز",
      },
      description: "أضف فرز لتنظيم صفوفك.",
      buttonLabels: {
        addSorting: "إضافة فرز",
        resetSorting: "إعادة تعيين الفرز",
      },
    },
    sortingItem: {
      fieldSelector: {
        searchPlaceholder: "بحث عن حقول...",
        noFieldsFound: "لم يتم العثور على حقول.",
      },
      directionSelector: {
        asc: "تصاعدي",
        desc: "تنازلي",
      },
    },
  },
}

export default toolbarDictionariesAr
