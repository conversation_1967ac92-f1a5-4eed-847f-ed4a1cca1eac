"use client"

import * as React from "react"
import { useFormContext } from "react-hook-form"

import { DataFormFieldProps } from "@/types/data-form"

import FileUpload from "../ui/file-upload"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form"
import { Skeleton } from "../ui/skeleton"
import DataFormFieldContainer from "./data-form-field-container"

interface DataFormFieldFileUploadProps<T = string>
  extends DataFormFieldProps<T> {
  /**
   * الحد الأقصى لعدد الملفات التي يمكن تحميلها. لن يكون لها تأثير الى اذا كان multiple = true
   * @default 3
   */
  maxFiles?: number
  /**
   * مصفوفة من أنواع ملفات أو ملحقاتها المقبولة.
   * @default  ["image/*"]
   */
  accept?: string[]
  /**
   * اذا كان صحيح, يسمح بتحميل عدة ملفات وستكون القيمة التي يرجعها الحقل هي مصفوفة لروابط الملفات التي تم تحميلها.
   *  اما اذا كان غير صحيح, سيسمح بتحميل ملف واحد فقط وستكون القيمة التي يرجعها هي نص لرابط الملف.
   * @default false
   */
  multiple?: boolean
  /**
   * إذا كان صحيح، يسمح بإعادة ترتيب الملفات التي تم تحميلها, لن يكون لها تأثير إلا إذا كان multiple = true.
   */
  allowReorder?: boolean
}

const DataFormFieldFileUpload = (props: DataFormFieldFileUploadProps) => {
  const { control } = useFormContext()

  const {
    label,
    name,
    description,
    disabled,
    placeholder,
    required,
    accept,
    allowReorder,
    maxFiles,
    multiple = false,
  } = props

  return (
    <FormField
      key={name}
      name={name}
      control={control}
      render={({ field }) => (
        <DataFormFieldContainer variant="fileUpload">
          <FormItem>
            <FormLabel className="pointer-events-none">
              {label}
              {required && <span className="text-destructive"> *</span>}
            </FormLabel>
            <FormControl>
              <FileUpload
                accept={accept}
                name={field.name}
                maxFiles={maxFiles}
                multiple={multiple}
                placeholder={placeholder}
                allowReorder={allowReorder}
                disabled={disabled ?? field.disabled}
                onChange={(val) => field.onChange(multiple ? val : val.at(0))}
                value={
                  Array.isArray(field.value) ? field.value : [field.value ?? ""]
                }
              />
            </FormControl>
            <FormDescription>{description}</FormDescription>
            <FormMessage />
          </FormItem>
        </DataFormFieldContainer>
      )}
    />
  )
}

const DataFormFieldFileUploadSkeleton = () => {
  return (
    <div className="col-span-2 flex min-h-[110px] w-full flex-col gap-2">
      <Skeleton className="h-[14px] w-40 max-w-[50%]" />
      <Skeleton className="h-19 w-full" />
    </div>
  )
}

export default DataFormFieldFileUpload
export { type DataFormFieldFileUploadProps, DataFormFieldFileUploadSkeleton }
