import * as React from "react"
import { useFormContext } from "react-hook-form"

import { DataFormFieldProps } from "@/types/data-form"

import { Checkbox } from "../ui/checkbox"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form"
import DataFormFieldContainer from "./data-form-field-container"

type DataFormFieldCheckboxProps<T = string> = Omit<
  DataFormFieldProps<T>,
  "placeholder"
>

const DataFormFieldCheckbox = (props: DataFormFieldCheckboxProps) => {
  const { control } = useFormContext()

  const { label, name, description, disabled, required } = props

  return (
    <FormField
      key={name}
      name={name}
      control={control}
      render={({ field }) => (
        <DataFormFieldContainer variant="checkbox">
          <FormItem className="flex w-full flex-row items-start gap-2">
            <FormControl>
              <Checkbox
                {...field}
                required={required}
                disabled={disabled ?? field.disabled}
                className="mt-px cursor-pointer"
                checked={field.value ?? false}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className="flex max-w-fit flex-col gap-1 leading-none">
              <FormLabel className="max-w-fit text-sm font-normal">
                {label}
                {required && <span className="text-destructive"> *</span>}
              </FormLabel>
              <FormDescription>{description}</FormDescription>
              <FormMessage />
            </div>
          </FormItem>
        </DataFormFieldContainer>
      )}
    />
  )
}

export default DataFormFieldCheckbox
export type { DataFormFieldCheckboxProps }
