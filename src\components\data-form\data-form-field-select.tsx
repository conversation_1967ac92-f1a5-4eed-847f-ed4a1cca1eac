import * as React from "react"
import { useFormContext } from "react-hook-form"

import { DataFormFieldProps } from "@/types/data-form"

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useFormField,
} from "../ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import DataFormFieldContainer from "./data-form-field-container"

interface DataFormFieldSelectOption<T = string> {
  label: string
  value: T
  icon?: React.FC<React.SVGProps<SVGSVGElement>>
  disabled?: boolean
}

interface DataFormFieldSelectProps<T = string, V = any>
  extends DataFormFieldProps<T> {
  options: DataFormFieldSelectOption<V>[]
  /**
   * عندما يكون صحيح, يتم عرض الحقل بشكل مرن.
   * بمعنى انه سيتم التحقق مما اذا كان هناك حقل قبل او بعد هذا العنصر إذا وجد الحقل سيتم عرض الحقلين في صف واحد
   * ملاحضة: سيتم عرض كل حقل في صف منفصل عندما تكون المساحة اقل من () حتا اذا كانت هذا الخاصية صحيحة وهذا يضمن عرض الحقول بشكل غير مزدحم في المساحات والشاشات الصغيرة
   * @default true
   */
  autoInline?: boolean
}

const DataFormFieldSelect = (props: DataFormFieldSelectProps) => {
  const { control } = useFormContext()
  const { formItemId } = useFormField()

  const {
    label,
    name,
    description,
    disabled,
    placeholder,
    required,
    options,
    autoInline = true,
  } = props

  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <DataFormFieldContainer variant="select" autoInline={autoInline}>
          <FormItem>
            <FormLabel>
              {label}
              {required && <span className="text-destructive"> *</span>}
            </FormLabel>
            <Select
              value={field.value ?? ""}
              defaultValue={field.value}
              onValueChange={field.onChange}
              disabled={disabled ?? field.disabled}
            >
              <FormControl>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
              </FormControl>
              <SelectContent id={formItemId}>
                {options.map((option) => (
                  <SelectItem
                    key={String(option.value)}
                    value={String(option.value)}
                    disabled={option.disabled}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormDescription>{description}</FormDescription>
            <FormMessage />
          </FormItem>
        </DataFormFieldContainer>
      )}
    />
  )
}

export default DataFormFieldSelect
export type { DataFormFieldSelectProps, DataFormFieldSelectOption }
