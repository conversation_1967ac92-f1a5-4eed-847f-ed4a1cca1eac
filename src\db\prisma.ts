import { PrismaClient } from "./generated"

// إعداد Prisma Client مع تحسينات الأداء
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient }

const prisma = globalForPrisma.prisma || new PrismaClient()
export default prisma

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma

// استثناء مفاتيح PrismaClient العامة
type InternalKeys =
  | "$connect"
  | "$disconnect"
  | "$on"
  | "$transaction"
  | "$use"
  | "$extends"
  | "$queryRaw"
  | "$queryRawUnsafe"
  | "$executeRaw"
  | "$executeRawUnsafe"
  | symbol

// طريقة استخراج انواع خصائص دالة findMany لجدول معين
// type test = Parameters<typeof prisma["admin"]["findMany"]>["0"]

export type PrismaModelNames = Exclude<keyof typeof prisma, InternalKeys>

// // دوال مساعدة للاستعلامات الشائعة
// export const queries = {
//   // الحصول على جميع النماذج المتقدمة مع العلاقات
//   async getAdvancedModelsWithRelations() {
//     return await prisma.advancedModel.findMany({
//       include: {
//         author: {
//           select: {
//             id: true,
//             username: true,
//             firstName: true,
//             lastName: true,
//             avatar: true,
//             role: true,
//           },
//         },
//         tags: true,
//         images: {
//           orderBy: { order: "asc" },
//         },
//         comments: {
//           where: { isApproved: true, isDeleted: false },
//           include: {
//             author: {
//               select: {
//                 id: true,
//                 username: true,
//                 firstName: true,
//                 lastName: true,
//                 avatar: true,
//               },
//             },
//             replies: {
//               include: {
//                 author: {
//                   select: {
//                     id: true,
//                     username: true,
//                     firstName: true,
//                     lastName: true,
//                     avatar: true,
//                   },
//                 },
//               },
//             },
//           },
//           orderBy: { createdAt: "desc" },
//         },
//         attachments: true,
//         categoryDetails: true,
//       },
//       where: {
//         isActive: true,
//         isDeleted: false,
//       },
//       orderBy: [
//         { isFeatured: "desc" },
//         { priority: "desc" },
//         { createdAt: "desc" },
//       ],
//     })
//   },

//   // البحث في النماذج المتقدمة
//   async searchAdvancedModels(searchTerm: string) {
//     return await prisma.advancedModel.findMany({
//       where: {
//         AND: [
//           { isActive: true },
//           { isDeleted: false },
//           { isPublished: true },
//           {
//             OR: [
//               { title: { contains: searchTerm, mode: "insensitive" } },
//               {
//                 shortDescription: { contains: searchTerm, mode: "insensitive" },
//               },
//               { content: { contains: searchTerm, mode: "insensitive" } },
//               { keywords: { contains: searchTerm, mode: "insensitive" } },
//             ],
//           },
//         ],
//       },
//       include: {
//         author: {
//           select: {
//             id: true,
//             username: true,
//             firstName: true,
//             lastName: true,
//             avatar: true,
//           },
//         },
//         tags: true,
//         images: {
//           where: { isMain: true },
//           take: 1,
//         },
//         categoryDetails: true,
//       },
//       orderBy: [
//         { isFeatured: "desc" },
//         { viewCount: "desc" },
//         { createdAt: "desc" },
//       ],
//     })
//   },

//   // الحصول على النماذج حسب الفئة
//   async getAdvancedModelsByCategory(category: string) {
//     return await prisma.advancedModel.findMany({
//       where: {
//         category: category as any,
//         isActive: true,
//         isDeleted: false,
//         isPublished: true,
//       },
//       include: {
//         author: {
//           select: {
//             id: true,
//             username: true,
//             firstName: true,
//             lastName: true,
//             avatar: true,
//           },
//         },
//         tags: true,
//         images: {
//           where: { isMain: true },
//           take: 1,
//         },
//       },
//       orderBy: [
//         { isFeatured: "desc" },
//         { priority: "desc" },
//         { createdAt: "desc" },
//       ],
//     })
//   },

//   // الحصول على الإحصائيات
//   async getStatistics() {
//     const [
//       totalModels,
//       totalUsers,
//       totalComments,
//       totalTags,
//       featuredModels,
//       activeUsers,
//     ] = await Promise.all([
//       prisma.advancedModel.count({
//         where: { isActive: true, isDeleted: false },
//       }),
//       prisma.user.count({
//         where: { isActive: true },
//       }),
//       prisma.comment.count({
//         where: { isApproved: true, isDeleted: false },
//       }),
//       prisma.tag.count({
//         where: { isActive: true },
//       }),
//       prisma.advancedModel.count({
//         where: { isFeatured: true, isActive: true, isDeleted: false },
//       }),
//       prisma.user.count({
//         where: {
//           isActive: true,
//           lastLoginAt: {
//             gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // آخر 30 يوم
//           },
//         },
//       }),
//     ])

//     return {
//       totalModels,
//       totalUsers,
//       totalComments,
//       totalTags,
//       featuredModels,
//       activeUsers,
//     }
//   },

//   // تحديث عدد المشاهدات
//   async incrementViewCount(modelId: string) {
//     return await prisma.advancedModel.update({
//       where: { id: modelId },
//       data: {
//         viewCount: { increment: 1 },
//         lastAccessedAt: new Date(),
//       },
//     })
//   },

//   // إضافة إعجاب
//   async incrementLikeCount(modelId: string) {
//     return await prisma.advancedModel.update({
//       where: { id: modelId },
//       data: {
//         likeCount: { increment: 1 },
//       },
//     })
//   },
// }

// // دوال التنظيف والصيانة
// export const maintenance = {
//   // تنظيف البيانات المحذوفة نهائياً
//   async cleanupDeletedData() {
//     const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

//     // حذف النماذج المحذوفة منذ أكثر من 30 يوم
//     const deletedModels = await prisma.advancedModel.deleteMany({
//       where: {
//         isDeleted: true,
//         updatedAt: { lt: thirtyDaysAgo },
//       },
//     })

//     // حذف التعليقات المحذوفة منذ أكثر من 30 يوم
//     const deletedComments = await prisma.comment.deleteMany({
//       where: {
//         isDeleted: true,
//         updatedAt: { lt: thirtyDaysAgo },
//       },
//     })

//     return {
//       deletedModels: deletedModels.count,
//       deletedComments: deletedComments.count,
//     }
//   },

//   // تحديث الإحصائيات
//   async updateAnalytics() {
//     const models = await prisma.advancedModel.findMany({
//       where: { isActive: true, isDeleted: false },
//       include: {
//         comments: { where: { isApproved: true, isDeleted: false } },
//         images: true,
//         attachments: true,
//       },
//     })

//     for (const model of models) {
//       await prisma.advancedModel.update({
//         where: { id: model.id },
//         data: {
//           analytics: {
//             totalViews: Number(model.viewCount),
//             totalComments: model.comments.length,
//             totalImages: model.images.length,
//             totalAttachments: model.attachments.length,
//             lastUpdated: new Date(),
//           },
//         },
//       })
//     }

//     return { updatedModels: models.length }
//   },
// }
