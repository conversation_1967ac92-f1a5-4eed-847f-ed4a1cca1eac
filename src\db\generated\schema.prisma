// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/db/generated"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Admin {
  id           String    @id @default(cuid())
  name         String
  email        String    @unique
  password     String
  role         AdminRole @default(STAFF)
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  lastLogin    DateTime?
  blockedPaths String[]  @default([]) // مسارات ممنوع الوصول لها

  // Optional metadata
  phone           String?
  profileImageUrl String?

  // Audit log (optional)
  createdBy String?
  updatedBy String?

  @@index([email])
}

enum AdminRole {
  SUPER_ADMIN
  ADMIN
  MODERATOR
  STAFF
}
