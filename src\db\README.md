# نموذج Prisma المتقدم

هذا المشروع يحتوي على نموذج Prisma متقدم يغطي جميع أنواع البيانات المختلفة مع بيانات seed شاملة.

## 📋 المحتويات

### النماذج (Models)

#### 1. AdvancedModel - النموذج الرئيسي المتقدم
يحتوي على جميع أنواع البيانات:

**النصوص (Strings):**
- `title` - عنوان قصير (255 حرف)
- `slug` - رابط فريد (100 حرف)
- `shortDescription` - وصف قصير (500 حرف)
- `longDescription` - وصف طويل (نص)
- `content` - المحتوى الرئيسي (نص)
- `metaTitle`, `metaDescription`, `keywords` - بيانات SEO

**الأرقام (Numbers):**
- `price`, `discountPrice` - أسعار (Decimal)
- `quantity`, `likeCount`, `shareCount` - أعداد صحيحة (Int)
- `weight`, `rating` - أرقام عشرية (Float)
- `viewCount` - عدد كبير (BigInt)

**القيم المنطقية (Booleans):**
- `isActive`, `isFeatured`, `isPublished`
- `isDeleted`, `allowComments`, `isPromoted`

**التعدادات (Enums):**
- `status` - حالة العنصر
- `priority` - أولوية العنصر
- `category` - فئة العنصر
- `userRole` - دور المستخدم

**التواريخ والأوقات (DateTime):**
- `createdAt`, `updatedAt` - تواريخ الإنشاء والتحديث
- `publishedAt`, `scheduledAt`, `expiresAt` - تواريخ مخصصة

**البيانات المنظمة (JSON):**
- `metadata` - بيانات وصفية
- `settings` - إعدادات
- `customFields` - حقول مخصصة
- `analytics` - بيانات التحليلات

**العلاقات (Relations):**
- `author` - المؤلف (User)
- `tags` - العلامات (Tag[])
- `images` - الصور (Image[])
- `comments` - التعليقات (Comment[])
- `attachments` - المرفقات (Attachment[])

#### 2. النماذج المساعدة

- **User** - المستخدمين
- **Tag** - العلامات
- **Image** - الصور
- **Comment** - التعليقات (مع دعم الردود)
- **Attachment** - المرفقات
- **CategoryDetail** - تفاصيل الفئات

### التعدادات (Enums)

```prisma
enum UserRole {
  ADMIN, MODERATOR, USER, GUEST
}

enum Status {
  ACTIVE, INACTIVE, PENDING, SUSPENDED, DELETED
}

enum Priority {
  LOW, MEDIUM, HIGH, URGENT
}

enum Category {
  TECHNOLOGY, BUSINESS, EDUCATION, ENTERTAINMENT, HEALTH, SPORTS, TRAVEL, FOOD
}
```

## 🚀 كيفية الاستخدام

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
npm run db:push

# توليد Prisma Client
npm run db:generate
```

### 3. تشغيل البيانات التجريبية (Seed)
```bash
npm run db:seed
```

### 4. فتح Prisma Studio لاستعراض البيانات
```bash
npm run db:studio
```

### 5. إعادة تعيين قاعدة البيانات (اختياري)
```bash
npm run db:reset
```

## 📊 البيانات التجريبية (Seed Data)

يتضمن ملف الـ seed البيانات التالية:

### المستخدمين (3 مستخدمين)
- **أحمد محمد** - مدير (ADMIN)
- **فاطمة علي** - مشرفة (MODERATOR)  
- **خالد السعد** - مستخدم عادي (USER)

### العلامات (5 علامات)
- تطوير الويب
- ذكاء اصطناعي
- تصميم UI/UX
- قواعد البيانات
- الأمن السيبراني

### تفاصيل الفئات (3 فئات)
- تقنية متقدمة
- أعمال وريادة
- تعليم وتدريب

### المحتوى المتقدم (2 عنصر)
1. **دليل شامل لتطوير تطبيقات الويب الحديثة**
   - محتوى كامل مع صور وتعليقات ومرفقات
   - بيانات تحليلية وإعدادات متقدمة

2. **أساسيات الذكاء الاصطناعي والتعلم الآلي**
   - دورة تدريبية متكاملة
   - إعدادات مختلفة وبيانات متنوعة

### البيانات المرتبطة
- **الصور**: صور من Unsplash مع بيانات وصفية
- **التعليقات**: تعليقات باللغة العربية مع نظام الإعجابات
- **المرفقات**: ملفات PDF وZIP مع إحصائيات التحميل

## 🔧 الميزات المتقدمة

### الفهرسة (Indexing)
- فهارس مركبة للبحث السريع
- فهارس على الحقول المهمة

### القيود (Constraints)
- قيود الفرادة على الحقول المهمة
- قيود المرجعية مع CASCADE

### أنواع البيانات المتخصصة
- `@db.VarChar()` للنصوص المحدودة
- `@db.Text` للنصوص الطويلة
- `@db.Decimal()` للأرقام المالية
- `@db.DoublePrecision` للأرقام عالية الدقة

## 📝 ملاحظات مهمة

1. **متطلبات البيئة**: تأكد من وجود متغير `DATABASE_URL` في ملف `.env`
2. **قاعدة البيانات**: النموذج مصمم لـ PostgreSQL
3. **الترميز**: جميع النصوص تدعم UTF-8 للغة العربية
4. **الأمان**: استخدم `CASCADE` بحذر في بيئة الإنتاج

## 🤝 المساهمة

يمكنك المساهمة في تطوير هذا النموذج من خلال:
- إضافة نماذج جديدة
- تحسين البيانات التجريبية
- إضافة فهارس جديدة
- تحسين الأداء
