import "server-only"

import React from "react"
// import { NextRequest } from "next/server"
// import { match as matchLocale } from "@formatjs/intl-localematcher"
// import Negotiator from "negotiator"

const dictionaries = {
  en: () => import("./dictionaries/en.json").then((module) => module.default),
  ar: () => import("./dictionaries/ar.json").then((module) => module.default),
}

/**
 * Get the dictionary for the given locale
 */
export const getDictionary = React.cache(async (locale: Locale) =>
  dictionaries[locale]()
)

export type DictionaryType = Awaited<ReturnType<typeof getDictionary>>

/**
 * I18n configuration
 */
export const i18n = {
  defaultLocale: "ar",
  locales: ["en", "ar"],
} as const

/**
 * Locale type
 */
export type Locale = (typeof i18n)["locales"][number]

// Get the preferred locale, similar to the above or using a library
// export function getLocale(request: NextRequest) {
//   const negotiatorHeaders: Record<string, string> = {}
//   request.headers.forEach((value, key) => (negotiatorHeaders[key] = value))

//   const locales = i18n.locales
//   const languages = new Negotiator({ headers: negotiatorHeaders }).languages()

//   const locale = matchLocale(languages, locales, i18n.defaultLocale)
//   return locale
// }

/**
 * Check if the pathname is missing the locale
 */
export const pathnameIsMissingLocale = (pathname: string) => {
  return i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  )
}
